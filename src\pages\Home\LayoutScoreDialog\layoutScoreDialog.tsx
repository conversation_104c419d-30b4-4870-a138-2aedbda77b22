
import { useEffect, useRef, useState } from "react";

import { TAppManagerBase } from "@/Apps/AppManagerBase";
import { EventName } from "@/Apps/EventSystem";
import { TLayoutEntityContainer } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TLayoutEntityContainter";
import { TRoomLayoutScheme } from "@/Apps/LayoutAI/Layout/TLayoutScheme/TRoomLayoutScheme";
import { I_LayoutScore, TLayoutJudgeContainter } from "@/Apps/LayoutAI/Layout/TLayoutScore/TLayoutJudge";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { PanelContainer } from "@svg/antd-cloud-design";

import useCommonStyles from "../common_style/index";
import LayoutScoreCard from "./layoutScoreCard";


const LayoutScoreDialog: React.FC = () => {

    const common_styles = useCommonStyles().styles;
    const [isDispaly, setIsDisplay] = useState<boolean>(false);
    const [layoutScoreList, setLayoutScoreList] = useState<I_LayoutScore[]>([]);
    const t = LayoutAI_App.t;
    const object_id = "LayoutScoreDialog";
    const timerRef = useRef(null);

    const register_events = () => {

    }

    register_events();

    let width = 265;
    let height = 600;

    const app = (LayoutAI_App.instance as TAppManagerBase);
    let container: TLayoutEntityContainer = app.layout_container;
    const update_layout_score = async () => {
        let selected_room = container._selected_room;

        if (!selected_room) {
            return;
        }

        if ((app._current_handler as any).selScheme) {
            const scheme = (app._current_handler as any).selScheme as TRoomLayoutScheme;
            let result = TLayoutJudgeContainter.ComputeSchemeScore(selected_room, scheme);
            setLayoutScoreList(result);
        } else {
            // 这里不传图元是不对的，但原始代码是这样，先保持旧的逻辑不变
            let result = TLayoutJudgeContainter.ComputeScoreInRoom(selected_room);
            setLayoutScoreList(result);
        }
    }

    const start_updating = () => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
        }
        timerRef.current = setInterval(() => {
            update_layout_score();
        }, 1000);

    }
    const stop_updating = () => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
        }
    }
    const showDialog = (t: boolean) => {
        setIsDisplay(t);
    }
    useEffect(() => {

        LayoutAI_App.on(EventName.ShowLayoutScoreDialog, (t: boolean = true) => {
            showDialog(t);
            if (t) {
                update_layout_score();
            }

        });

        LayoutAI_App.on_M(EventName.UpdateLayoutScore, object_id, (t: boolean = true) => {
            update_layout_score();
        })
        // 设置定时器
        // start_updating();

        // 返回一个清理函数
        return () => {
            // 组件卸载时清除定时器
            // clearInterval(timerRef.current);
        };
    }, []);

    return (
        <>
            {isDispaly &&
                <PanelContainer title={t("布局评分器")} right={250} width={width} height={height} resizable={true} draggable={true} onClose={() => {
                    showDialog(false);
                }} bodyStyle={{ background: "#ffffff", border: "0", boxShadow: "0" }}>
                    <LayoutScoreCard layoutScoreList={layoutScoreList} style={0}></LayoutScoreCard>

                    {/* <div className={common_styles.layoutScoreContent}>
                <div className='room_title'>
                     {t("当前空间")+"： "+ (t(container?._selected_room?.name)||"")}
                </div>
            </div> */}

                </PanelContainer>}
        </>

    )
}

export default LayoutScoreDialog;