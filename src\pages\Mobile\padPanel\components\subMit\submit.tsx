    import React from 'react';
    import { observer } from 'mobx-react-lite';
    import useStyles from './style/index';
    import { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
    import { useStore } from '@/models';
    import IconFont from '@/components/IconFont/iconFont';
    import { TViewCameraEntity } from '@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity';
    import { I_Scene3D, LayoutAI_App, LayoutAI_Events } from '@/Apps/LayoutAI_App';
    import { TAppManagerBase } from '@/Apps/AppManagerBase';
    import { InputNumber, message, Slider, Tag } from '@svg/antd';
    import { Scene3DEvents } from '@/Apps/LayoutAI/Scene3D/Scene3DEvents';
    import { AutoLightingService } from '@/Apps/LayoutAI/Services/AutoLighting/AutoLightService';
    import { FigureViewControls } from '@/Apps/LayoutAI/Scene3D/controls/FigureViewControls';
    import { BaseControls } from '@/Apps/LayoutAI/Scene3D/controls/BaseControls';
    import { Scene3D } from '@/Apps/LayoutAI/Scene3D/Scene3D';
    import { Camera, PerspectiveCamera, Vector3, Vector3Like } from 'three';
    import { EventName } from '@/Apps/EventSystem';
    import { ServerRenderService } from '@/Apps/LayoutAI/Services/ServerRender/ServerRenderService';
    import Icon from '@/components/Icon/icon';
    import { CSSTransition } from 'react-transition-group';
    import ViewSelect from '../ViewSelect/viewSelect';
    import {
    IResolutionConfig,
    PanoResolutionConfig,
    ResolutionConfig,
    ResolutionTag
    } from '@/Apps/LayoutAI/Services/ServerRender/OfflineRenderType';
    import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
    import { drawingPicMode } from '../ExitBar'
import { SdkService } from '@/services/SdkService';
import { insertViewEffects } from '@/services/padMobile';
import { getPollingService } from '@/services/RenderPollingService';
import { I_SwjViewCameraData } from '@layoutai/basic_data';

    
// 定义组件的 ref 类型
export interface SubmitRef {
    clickSubmit: () => Promise<void>;
}

const Submit: React.FC<{ ref?: React.Ref<SubmitRef> }> = forwardRef<SubmitRef>((props, ref) => {
    const { styles } = useStyles();
    const store = useStore();
    let { drawPictureMode, aspectRatioMode, setAspectRatioMode } = store.homeStore;
    const { CheckableTag } = Tag;
    const contentBoxRef = useRef<HTMLDivElement>(null);
    const [resolutionTag, setResolutionTag] = useState<number>(1); //分辨率
    const [panoResolutionTag, setPanoResolutionTag] = useState<number>(1); //分辨率
    const [nearValue, setNearValue] = useState<number>(300);
    const pollingService = getPollingService(store.homeStore);

    const { guideMapCurrentRoom } = store.homeStore;
    const [previousValue, setPreviousValue] = useState(20);
    const marks = {
        20: {
        label: '特写'
        },
        40: {
        label: '人眼'
        },
        65: {
        label: '标准'
        },
        90: {
        label: '广角'
        }
    };
    const RadioButton: { lable: string; radioMode: number }[] = [
        {
        lable: '4:3',
        radioMode: 1
        },
        {
        lable: '16:9',
        radioMode: 2
        },
        {
        lable: '3:4',
        radioMode: 3
        },
        {
        lable: '9:16',
        radioMode: 4
        },
        {
        lable: '原图',
        radioMode: 5
        }
    ];

    const resolutionButton: {
        lable: string;
        radioMode: number;
        config: IResolutionConfig;
    }[] = [
        {
        lable: '标清',
        radioMode: 1,
        config: ResolutionConfig[ResolutionTag.SD]
        },
        {
        lable: '高清',
        radioMode: 2,
        config: ResolutionConfig[ResolutionTag.HD]
        },
        // {
        //     lable: "超清",
        //     radioMode: 3,
        //     config: ResolutionConfig[ResolutionTag.FHD]
        // },
        // {
        //     lable: "4K",
        //     radioMode: 4,
        //     config: ResolutionConfig[ResolutionTag._4K]
        // }
    ];

    const panoResolutionButton: {
        lable: string;
        radioMode: number;
        config: IResolutionConfig;
    }[] = [
        {
            lable: '4K',
            radioMode: 1,
            config: PanoResolutionConfig[ResolutionTag._4K]
        },
        {
            lable: '6K',
            radioMode: 2,
            config: PanoResolutionConfig[ResolutionTag._6K]
        }
    ];

    // 控制标准渲染模式下的提交出图
    const handleSubmitRender = async (resolution?: string, camera?: Camera): Promise<{ success: boolean, msg: string, queueId: string, schemeId: string }> => {
        // 修改分辨率
        if (resolution) {
            ServerRenderService.resolutionTag = resolution as ResolutionTag;
        }
        // 离线渲染
        const res = await ServerRenderService.commitOfflineRender(camera);

        message.destroy();
        if(!res.success){
            if(res.msg === '余额不足，请充值！'){
                message.error(
                    <>
                        提交渲染失败，余额不足，请点击
                        <a href="https://mall.3vjia.com/" target="_blank" rel="noopener noreferrer">
                            购买
                        </a>
                    </>,
                    5
                )
                return res;
            }
            message.error(
                <>
                    {res.msg}
                </>,
                3
            )
        } else {
            message.success('提交渲染成功！');
            pollingService.checkAndStartPolling(); // 开启渲染任务进度轮询
        }

        return res;

        // if (res.success) {
        //     await store.homeStore.checkAndStartPolling()
        //     const layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        //     message.destroy();
        //     message.success('提交渲染成功！');
        // } else {
        //     message.destroy();
        //     // message.error(res.msg);
        //     message.error(
        //         <>
        //             提交渲染失败，余额不足，请点击
        //             <a href="https://mall.3vjia.com/" target="_blank" rel="noopener noreferrer">
        //                 购买
        //             </a>
        //         </>,
        //         5
        //     )
        //     console.log("渲染提交报错信息：", res.msg)
        // }
    };


    const _checkViewCameraValid = (_target_view_entity: TViewCameraEntity, scene3d: I_Scene3D) => {
        if (_target_view_entity) {
            let pp = _target_view_entity.rect.project(scene3d.camera.position);
            let minY = -300; let maxY = 1800;
            let minX = -300; let maxX = 300;
            if(pp.x >= minX && pp.x <= maxX && pp.y >= minY && pp.y <=maxY)
            {
                return true;
            }
        }
        return false;
    }

    /**
     * @description 关联渲染图到视角中
    */
    const insertRenderImageToView = async (queueId: string) => {
        let scene3d = (LayoutAI_App.instance as TAppManagerBase).scene3D;
        let container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
        const prams = {
            layoutSchemeId: container._layout_scheme_id,
            queueFlag: 0,
            queueId: queueId,
            roomId: 0,
            roomName: '',
            viewAngleData: null as I_SwjViewCameraData,
            viewAngleId: '',
            viewAngleName: '',
        }

        if(_checkViewCameraValid(store.homeStore.currentViewCameraEntity, scene3d))
        {
            prams.roomId = store.homeStore.currentViewCameraEntity._room_entity.uidN;
            prams.roomName = store.homeStore.currentViewCameraEntity._room_entity.roomname;
            prams.viewAngleData = store.homeStore.currentViewCameraEntity.exportData() as I_SwjViewCameraData;
            prams.viewAngleId = store.homeStore.currentViewCameraEntity.ukey;
            prams.viewAngleName = store.homeStore.currentViewCameraEntity.name;
        } else 
        {
            
            // 自由视角,先以当前坐标所在的空间为基础，获取空间信息
            console.log('自由视角',store.homeStore.guideMapCurrentRoom);
            prams.roomId = store.homeStore.guideMapCurrentRoom._room_entity.uidN;
            prams.roomName = store.homeStore.guideMapCurrentRoom._room_entity.roomname;
            prams.viewAngleId = '-1';
            prams.viewAngleName = '自由视角';
        }
        const res = await insertViewEffects(prams);
        if(res.success)
        {
            // message.success('关联渲染图到视角成功！');
        }
    }


    const handleSubmitAIDraw = async () => {
        message.loading('提交渲染中...', 0);
        LayoutAI_App.emit_M(Scene3DEvents.AiDrawingCapture, true);
    }

    // const checkCoupon = async (resolutionType_forCoupon: string): Promise<boolean> => {
    //     try {
    //     const res = await RenderReqOffline.instance.getCoupon(resolutionType_forCoupon);

    //     if (!res) {
    //         throw new Error();
    //     }

    //     if (res.surplusUseCount < 1) {
    //         message.warning(
    //         <>
    //             提交渲染失败，余额不足，请点击
    //             <a href="https://mall.3vjia.com/" target="_blank" rel="noopener noreferrer">
    //             购买
    //             </a>
    //         </>
    //         );
    //         return false;
    //     }
    //     return true;
    //     } catch (error) {
    //     message.error('获取渲染券失败，请稍后重试。');
    //     return false;
    //     }
    // };

    const clickSubmit = async () => {
        // 海尔埋点
        SdkService.clickRenderBtn();
        if (drawPictureMode === drawingPicMode.aiDrawing) {
            LayoutAI_App.emit_M(Scene3DEvents.AiDrawingCapture, true);
        } else if (drawPictureMode === drawingPicMode.render) {
            // 根据选中的 resolutionTag 获取对应的分辨率值
            const selectedResolution = resolutionButton.find(
                tag => tag.radioMode === resolutionTag
            )?.config?.resolutionTag;
            console.log(drawPictureMode, selectedResolution);
            if (!selectedResolution) {
                message.error('未选择有效的分辨率！');
                return;
            }

            LayoutAI_App.instance.renderSubmitObject = {
                drawPictureMode: drawPictureMode,
                radioMode: aspectRatioMode,
                resolution: resolutionTag
            };


            message.loading('提交渲染中...', 0);
            let interval: any = null;
            const layoutContainer = (LayoutAI_App.instance as TAppManagerBase).layout_container;
            if (!layoutContainer._layout_scheme_id) {
                LayoutAI_App.DispatchEvent(LayoutAI_Events.autoSave, null);
    
                // 清除已有定时器避免重复创建
                if (interval) clearInterval(interval);
                
                interval = setInterval(async () => {
                    if (layoutContainer._layout_scheme_id) {
                    // 执行前立即清除定时器
                    clearInterval(interval!);
                    interval = null;
                    // 动态传入分辨率值
                    const res = await handleSubmitRender(selectedResolution);
                    if(res.success)
                    {
                        insertRenderImageToView(res.queueId);
                    }
                    message.destroy();
                    }
                }, 500);
                return;
            } else
            {
                // 动态传入分辨率值
                const res = await handleSubmitRender(selectedResolution);
                if(res.success)
                {
                    insertRenderImageToView(res.queueId);
                }
            }
        } else if (drawPictureMode === drawingPicMode.panoRender) {
            // 判断是否可以渲染
            const manager = LayoutAI_App.instance as TAppManagerBase;
            const scene3d = manager.scene3D;
            const controls = scene3d.active_controls as FigureViewControls;
            const position = controls.getViewPosition(1);

            let targetRoom = null;
            for (let room of manager.layout_container._rooms) {
                if (room.room_shape._poly.containsPoint(position)) {
                    targetRoom = room;
                    break;
                }
            }
            // 1. 检查是否在墙外
            if (!targetRoom) {
                message.warning('您的相机在墙外，请把视角前移');
                return;
            }
            // 2. 检查是否与家具重叠
            let furnitureList = targetRoom._room_entity.getFurnitureEntitiesOnFlat();
            let hasCollision = false;
            for (let furniture of furnitureList) { 
                let overlapRect = furniture.rect.intersect_rect(controls.viewRect);
                if (furniture.pos_z + furniture.height <= controls.viewRect.zval ||
                    furniture.pos_z >= controls.viewRect.zval
                ) continue;
                if (overlapRect && overlapRect.area > 0) {
                    hasCollision = true;
                    break;
                }
            }
            if (hasCollision) {
                message.warning(`您的相机与家具重叠，请重新调整视角`);
                return;
            }

            // 调整渲染时视角垂直 利用临时相机修改渲染参数使前端不响应
            const camera = controls.camera.clone();
            const rect = controls._rect;
            if (!rect) return;
            const t_nor = rect.nor.clone();
            t_nor.applyAxisAngle(rect.dv, 0); // rect.rotation_x = 0
            const target = new Vector3().copy(rect.rect_center_3d as Vector3Like).add(t_nor.multiplyScalar(10));
            camera.lookAt(target);
            camera.updateMatrix();
            
            // 提交渲染
            const selectedResolution = panoResolutionButton.find(
                tag => tag.radioMode === panoResolutionTag
            )?.config?.resolutionTag;
            message.loading('提交渲染中...', 0);
            let res =  await handleSubmitRender(selectedResolution, camera);
            if (res && res.success) {
                let layout_container = (LayoutAI_App.instance as TAppManagerBase).layout_container;
                let schemeId = layout_container._layout_scheme_id;
                let queueId = res.queueId;
                let url = await ServerRenderService.getOfflineRenderUrl(schemeId, queueId);
                console.log("全景渲染图URL: ", url);
            }
        }
    }
    useImperativeHandle(ref, () => ({
        clickSubmit: clickSubmit,
    }));
    return (
        <>
            {/* 因为设置海尔进入会默认设置isdrawPicture为true，但是这里不需要展示视角和提交渲染，保留相机的弹窗 */}
            {!store.userStore.isHaiEr && <div className={styles.submitContainer}>
                <ViewSelect />
                <div className={styles.submitBtn}>
                    <div
                        className={'submit'}
                        onClick={clickSubmit}
                    >
                        提交渲染
                    </div>
                </div>
            </div>}

            <CSSTransition
                in={store.homeStore.showSubmitInfo}
                timeout={300}
                classNames={{
                enter: 'fadeEnter',
                enterActive: 'fadeEnterActive',
                exit: 'fadeExit',
                exitActive: 'fadeExitActive'
                }}
                unmountOnExit
            >
                <div className={styles.submitInfo}>
                <div className={styles.titleTag}>
                    <div>
                        {drawPictureMode == drawingPicMode.aiDrawing
                            ? 'AI绘图'
                            : drawPictureMode == drawingPicMode.render
                            ? '标准渲染'
                            : '全景渲染'}
                    </div>
                    <Icon
                    onClick={() => {
                        store.homeStore.setShowSubmitInfo(false);
                    }}
                    style={{ fontSize: 14, cursor: 'pointer' }}
                    iconClass="icon-icon"
                    />
                </div>
                {drawPictureMode !== drawingPicMode.panoRender && (
                    <>
                    <div className={styles.label}>构图</div>
                    <div style={{ marginBottom: 20 }}>
                        {RadioButton.map(tag => (
                        <CheckableTag
                            key={tag.radioMode}
                            checked={aspectRatioMode == tag.radioMode}
                            onChange={checked => {
                            setAspectRatioMode(tag.radioMode);
                            }}
                        >
                            {tag.lable}
                        </CheckableTag>
                        ))}
                    </div>
                    <div className={styles.label}>视角</div>
                    <div className={styles.lensContainer}>
                        <div style={{ marginTop: 12, marginRight: 20, color: 'rgba(255, 255, 255, 0.80)' }}>
                        镜头
                        </div>
                        <div>
                        <Slider
                            style={{ width: '160px' }}
                            marks={marks}
                            defaultValue={65}
                            step={1}
                            min={20}
                            max={90}
                            onChange={(value: any) => {
                            // 根据 Slider 的值设置 _moving_offset
                            //  const prevValue = previousValue;
                            //  setPreviousValue(value); // 更新之前的值
                            //  // 判断拖动方向
                            //  const direction = value > prevValue ? -1 : 1; // 向右为-1，向左为1
                            //  let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D;
                            //  let controls = scene3D.active_controls as FigureViewControls;
                            //  // 根据方向设置 _moving_offset.y
                            //  controls._moving_offset.y = 3 * direction; // 向右为-2，向左为+2
                            //  // 调用更新方法
                            //  controls.updateByFigureElement();
                            //  // 重置 _moving_offset
                            //  controls._moving_offset.set(0, 0, 0);
                            let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D;
                            let controls = scene3D.active_controls as FigureViewControls;
                            let camera = controls.camera as PerspectiveCamera;
                            camera.fov = value;
                            camera.updateProjectionMatrix();
                            LayoutAI_App.emit_M(EventName.Scene3DCameraChanged, this);
                            }}
                        ></Slider>
                        </div>
                    </div>
                    <div className={styles.lensContainer}>
                        <div style={{ marginTop: 12, marginRight: 20, color: 'rgba(255, 255, 255, 0.80)' }}>
                        裁剪
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 20 }}>
                        <Slider
                            style={{ width: '120px' }}
                            min={300}
                            max={2500}
                            step={10}
                            defaultValue={300}
                            onChange={(value: any) => {
                                let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D;
                                let controls = scene3D.active_controls as FigureViewControls;
                                let camera = controls.camera as PerspectiveCamera;
                                camera.near = value;
                                camera.updateProjectionMatrix();
                                LayoutAI_App.emit_M(EventName.Scene3DCameraChanged, this);
                                setNearValue(value);
                            }}
                        ></Slider>
                        <InputNumber
                            min={300}
                            max={2500}
                            step={10}
                            value={nearValue}
                            style={{ width: '70px' }}
                            onChange={(value: any) => {
                                let scene3D = (LayoutAI_App.instance as TAppManagerBase).scene3D;
                                let controls = scene3D.active_controls as FigureViewControls;
                                let camera = controls.camera as PerspectiveCamera;
                                camera.near = value;
                                camera.updateProjectionMatrix();
                                LayoutAI_App.emit_M(EventName.Scene3DCameraChanged, this);
                                setNearValue(value);
                            }}
                        ></InputNumber>
                        </div>
                    </div>
                    </>
                )}

                {drawPictureMode == drawingPicMode.render && (
                    <>
                    <div className={styles.label}>分辨率</div>
                    <div style={{ marginBottom: 20 }}>
                        {resolutionButton.map(tag => (
                        <CheckableTag
                            key={tag.radioMode}
                            checked={resolutionTag == tag.radioMode}
                            onChange={async () => {
                                if (resolutionTag === tag.radioMode) {
                                    return; // 如果当前分辨率已选中，则不执行后续逻辑
                                }

                                // if (tag.radioMode === 2) {
                                //     const hasCoupon = await checkCoupon(tag.config.resolutionType);
                                //     if (!hasCoupon) return;
                                // }
                                setResolutionTag(tag.radioMode);
                            }}
                        >
                            {tag.lable}
                        </CheckableTag>
                        ))}
                    </div>
                    </>
                )}

                {drawPictureMode == drawingPicMode.panoRender && (
                    <>
                    <div className={styles.label}>分辨率</div>
                    <div style={{ marginBottom: 20 }}>
                        {panoResolutionButton.map(tag => (
                        <CheckableTag
                            key={tag.radioMode}
                            checked={panoResolutionTag == tag.radioMode}
                            onChange={async () => {
                                if (panoResolutionTag === tag.radioMode) {
                                    return;
                                }
                                setPanoResolutionTag(tag.radioMode);
                            }}
                        >
                            {tag.lable}
                        </CheckableTag>
                        ))}
                    </div>
                    </>
                )}
                </div>
            </CSSTransition>
        </>
    );
});

export default observer(Submit);
