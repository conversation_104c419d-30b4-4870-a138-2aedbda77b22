import { Box3, Float32<PERSON>ufferAttribute, Group, Matrix4, Mesh, MeshStandardMaterial, Object3D, Vector3 } from "three";
import { GLTFLoader } from "three/examples/jsm/Addons.js";
import { generateUUID } from "three/src/math/MathUtils.js";

import { getImgDomain } from "@svg/request";

import { DesignXmlParser } from "../AICadData/DesignXmlParser";
import { I_SwjCabinetData, StyleBrush } from "../AICadData/SwjLayoutData";
import { I_DesignMaterialInfo, I_MaterialMatchingItem, isGroupDesignMaterialInfo } from "../Layout/IMaterialInterface";
import { GeometryBuilder } from "../Scene3D/builder/GeometryBuilder";
import { MeshBuilder } from "../Scene3D/MeshBuilder";
import { MeshName, UserDataKey } from "../Scene3D/NodeName";
import { StyleMaterialDict } from "../Scene3D/StyleMaterialDict";
import { MaterialService } from "../Services/MaterialMatching/MaterialService";
import { compareNames } from "@layoutai/z_polygon";
import { get_swj_xml_from_url } from "../Utils/xml_utils";
import { ZRect } from "@layoutai/z_polygon";
import { Cabinet3DApi, I_RawGltfJson, SimpleGlbParser, SimpleSVJParser, SvgGltfCabinetNode,MaterialManager, SvgGltfNode, GltfManager, SvgGltfSwingDoorLeafNode, LayoutCustomCabinetInfoService } from "@layoutai/model3d_api";
import { getReplacementData } from "@/services/home";
import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { TAppManagerBase } from "@/Apps/AppManagerBase";


type MaterialInfoOptions = {
    target_rect?: ZRect, category?: string,
    target_size?: { length: number, width: number, height: number },
    cabinet_board_texture_img?: HTMLImageElement,
    onLoadEnd?: (node3d: Group) => void,
    isWhite?: boolean,
    isSubModel?: boolean, // 是否是子模型，子模型不添加代理box，只有最外层的模型添加代理box
    accurateGlbUrl?: string,
    similarGlbUrl?: string,
    alignTopZval?: number,
    uidN?: string|number,
}

export class Model3dApi {
    // 控制是否使用 glb 模型的定制柜
    public static isGlbCabinet = true;
    // 控制是否使用预存柜体
    public static isPreSaveCabinet = true;

    public static forceLoadGlbDataDirectly: boolean = false

    static async MakeMesh3DWithDesignMaterialInfo(design_material_info: I_DesignMaterialInfo, options: MaterialInfoOptions = {}) {
        if (!design_material_info) return null;

        let target_size = options.target_size || { length: 1000, width: 1000, height: 1000 };
        let category = options.category || "";


        const getPICValue = (val: string | number, default_val: number) => {
            if (!val) return default_val;
            let n_val = parseFloat('' + val);
            if (Math.abs(n_val) > 0.1) return n_val;
            return default_val;
        }
        let m_ll = target_size.length;
        let m_dd = target_size.width;
        let m_hh = target_size.height;

        m_ll = getPICValue(design_material_info?.PICLength, m_ll);
        m_dd = getPICValue(design_material_info?.PICWidth, m_dd);
        m_hh = getPICValue(design_material_info?.PICHeight, m_hh);
        let target_rect = options.target_rect || new ZRect(m_ll, m_dd);

        let rect = target_rect;
        let isWhite = !!options.isWhite;

        if (design_material_info.A3dSource) {
            let isGlb = false;

            let group_node = await Model3dApi.MakeMesh3D_WithA3dSource(
                design_material_info.MaterialId,
                design_material_info.A3dSource,
                isGlb,
                isWhite,
            );
            if (!group_node && design_material_info.isGlb) {
                // 如果 svg 获取或解析失败，则尝试 glb 模型
                // console.warn("svj 获取或解析失败，尝试 glb 模型", design_material_info.MaterialId,design_material_info.MaterialName);
                // group_node = await Model3dApi.MakeMesh3D_WithA3dSource(
                //     design_material_info.MaterialId,
                //     design_material_info.A3dSource,
                //     true,
                //     isWhite,
                // );
            }

            if (group_node) {

                group_node.userData[UserDataKey.FurnitureType] = MeshName.Model;
                group_node.userData[UserDataKey.MaterialId] = design_material_info.MaterialId;
                Model3dApi.UpdatePoseOfModel(group_node, options);
            } else {
                console.warn("group_node a3dis null", design_material_info.MaterialId, design_material_info.A3dSource);
            }

            return group_node;

        }
        else if (design_material_info.ContentUrl) {
            // 控制是否使用 glb 模型的定制柜
            let group_node;
            if (Model3dApi.isGlbCabinet) {
                let glbDescUrl: string = null;
                let glbDescJson: I_RawGltfJson = null;

                if (!Model3dApi.forceLoadGlbDataDirectly) {
                    // if (options.similarGlbUrl) {
                    //     let glbUrl = options.similarGlbUrl;
                    //     if (glbUrl.includes(".glb")) {
                    //         let underlineIndex = glbUrl.lastIndexOf("_");
                    //         if (underlineIndex > 0) {
                    //             glbDescUrl = glbUrl.substring(0, underlineIndex) + "_0.json";
                    //         }
                    //     } else if (glbUrl.includes(".json")) {
                    //         let underlineIndex = glbUrl.lastIndexOf("_");
                    //         if (underlineIndex > 0) {
                    //             glbDescUrl = glbUrl.substring(0, underlineIndex) + "_0.json";
                    //         }
                    //         glbDescUrl = glbUrl;
                    //     }
                    //     if (glbDescUrl) {
                    //         try {
                    //             // console.log(design_material_info.MaterialName, glbDescUrl);
                    //             glbDescUrl += "?t=10001";
                    //             glbDescJson = await fetch(glbDescUrl).then(val => val.json());
                    //         } catch (error) {
                    //             console.error("fetch glbDescJSon by similarGlbUrl error", error);
                    //         }
                    //     }
                    // }

                    if (glbDescJson == null) {
                        // console.warn("The glb desc json from matched material is null.",glbDescUrl);

                        let useSize = {...target_size};
                        if(design_material_info && compareNames([options.category],["地柜","吊柜","高柜","餐边柜"])) // 主要用原始尺寸
                        {
                            useSize.length = design_material_info.PICLength;
                            useSize.width = design_material_info.PICWidth;
                            useSize.height = design_material_info.PICHeight;
                        }
                        let glbInfos = await LayoutCustomCabinetInfoService.querySimilarInfos({
                            materialId: design_material_info.MaterialId, pageIndex: 1,styleId:"0",
                        }, { width: useSize.length, height: useSize.height, depth: useSize.width });

                        // console.log(design_material_info.MaterialName, design_material_info.MaterialId, glbInfos);
                        if (glbInfos[0]?.valueUrl) {
                            let valueUrl = glbInfos[0].valueUrl;
                            valueUrl = valueUrl.replace(".glb",".json");
                            if (valueUrl.includes(".json")) {
                                glbDescUrl = valueUrl;
                                if (!glbDescUrl.startsWith("http")) {
                                    glbDescUrl = "https://3vj-content.3vjia.com/" + glbDescUrl;
                                }
                            }

                            if (glbDescUrl) {
                                try {
                                    // console.log(design_material_info.MaterialName, glbDescUrl);
                                    glbDescUrl += "?t=10001";
                                    glbDescJson = await fetch(glbDescUrl).then(val => val.json());
                                } catch (error) {
                                    console.error("fetch glbDescJSon by similarGlbUrl error", error,glbDescUrl);
                                }
                            }
                        }
                    }
                    if (glbDescJson != null) {
                        let svgGltfNode = new SvgGltfCabinetNode(glbDescJson);

                        svgGltfNode.makeSvgGltfNodes();
                        svgGltfNode.updateCabinetsBox();
                        await svgGltfNode.updateSolidModels();
                        group_node = svgGltfNode;
                        let items = glbDescUrl.split("_");
                        let sizeCode = items[items.length - 2];
                        let vals = sizeCode.split("X");
                        group_node.userData.glbDescUrl = glbDescUrl;
                        if (!category.includes("高柜") && sizeCode.includes("X") && vals.length == 3) {
                            let cabinet_length = parseInt(vals[0]);
                            let cabinet_wdith = parseInt(vals[1]);
                            let cabinet_height = parseInt(vals[2]);
                            let cabinet_data = { length: cabinet_length, width: cabinet_wdith, height: cabinet_height };
                            group_node.userData.cabient_data = cabinet_data;
                            // 
                            // let mainCabinetBox3 = svgGltfNode.mainCabinetBox3;
                            // if (mainCabinetBox3) {
                            //     let size = mainCabinetBox3.getSize(new Vector3());

                            //     cabinet_data.length = size.x;
                            //     cabinet_data.height = size.z;
                            // }
                        }
                        else {
                            let mainCabinetBox3 = svgGltfNode.mainCabinetBox3;
                            if (mainCabinetBox3) {
                                let size = mainCabinetBox3.getSize(new Vector3());

                                let cabinet_data = { length: size.x, width: size.y, height: size.z };
                                group_node.userData.cabient_data = cabinet_data;


                            }
                        }

                        group_node.traverse((node) => {
                            let mesh = (node as Mesh);
                            if (mesh.isMesh) {
                                MeshBuilder.bindMeshMaterials(mesh);
                            }
                        })


                        // // 在这里请求柜子接口，获取柜子替换数据，回显到3D中
                        // let res = await getReplacementData({
                        //     id: design_material_info.MaterialId,
                        //     replaceMtlId: options.uidN,
                        //     layoutSchemeId: (LayoutAI_App.instance as TAppManagerBase)?.layout_container._layout_scheme_id,
                        // });
                        // if(res && res.result.length > 0 && res.result[0]?.replaceMtlData)
                        //     {
                        //         let json = await fetch(res.result[0].replaceMtlData).then(val=>val.json());
                        //         // 将数组格式转换为Map格式
                        //         const convertedMappings = {
                        //           furnitureNodes: new Map(),
                        //           doorLeafTextureNodes: new Map(),
                        //           doorLeafNodes: new Map(),
                        //         };
                        //         // 转换电器替换数据
                        //         if (json.furnitureNodes && Array.isArray(json.furnitureNodes)) {
                        //           json.furnitureNodes.forEach((item: any) => {
                        //             if (item.uidN) {
                        //               convertedMappings.furnitureNodes.set(item.uidN.toString(), item);
                        //             }
                        //           });
                        //         }
                        //         // 转换门板材质替换数据
                        //         if (json.doorLeafTextureNodes && Array.isArray(json.doorLeafTextureNodes)) {
                        //           json.doorLeafTextureNodes.forEach((item: any) => {
                        //             if (item.uidN) {
                        //               convertedMappings.doorLeafTextureNodes.set(item.uidN.toString(), item);
                        //             }
                        //           });
                        //         }
                        //         // 转换门板素材替换数据
                        //         if (json.doorLeafNodes && Array.isArray(json.doorLeafNodes)) {
                        //           json.doorLeafNodes.forEach((item: any) => {
                        //             if (item.uidN) {
                        //               convertedMappings.doorLeafNodes.set(item.uidN.toString(), item);
                        //             }
                        //           });
                        //         }
                        
                        //         console.log('转换后的替换数据:', convertedMappings);

                        //         if (convertedMappings) {
                        //             // 回显替换门板
                        //             svgGltfNode.loadGLBParserWithReplacement(svgGltfNode,convertedMappings);
                        //         }
                        // }


                    }
                    else {
                        console.warn(options.category, "no json");
                    }
                }

            }
            if (group_node == null) {
                group_node = await Model3dApi.MakeMesh3D_WithContentUrl(
                    design_material_info.MaterialId,
                    design_material_info.ContentUrl,
                    category, options?.onLoadEnd,
                    isWhite);
            }
            if (group_node) {
                group_node.userData[UserDataKey.MaterialId] = design_material_info.MaterialId;
                group_node.userData[UserDataKey.FurnitureType] = MeshName.CustomModel;
                group_node.userData[UserDataKey.FigureMeshTypeName] = MeshName.SolidMaterial;
                Model3dApi.UpdatePoseOfModel(group_node, options);
            } else {
                console.warn("group_node cabinet is null", design_material_info.MaterialId, design_material_info.ContentUrl);
            }

            return group_node;
        }

        return null;
    }

    static async UpdatePoseOfModel(group_node: Group, options: MaterialInfoOptions = {}) {
        if (!group_node) return;
        let target_size = options.target_size || { length: 1000, width: 1000, height: 1000 };
        let target_rect = options.target_rect || new ZRect(target_size.length, target_size.width);
        let category = options.category || "";

        let rect = target_rect;
        if (group_node.userData[UserDataKey.FurnitureType] === MeshName.Model) {
            let box = group_node.userData[UserDataKey.BoundingBox] || null;
            if (!box) {
                box = new Box3();
                box.setFromObject(group_node);
                group_node.userData[UserDataKey.BoundingBox] = box;
            }
            let matrix = new Matrix4();
            let center = box.getCenter(new Vector3());
            let box_length = box.max.x - box.min.x;
            let box_width = box.max.y - box.min.y;
            let box_height = box.max.z - box.min.z;

            let t_hh = target_size.height || box_height;

            let rotation_z = 0;
            if (category.endsWith("窗") || category.endsWith("门")) {
                rotation_z = rect.rotation_z + Math.PI;

            }
            else {
                rotation_z = rect.rotation_z;
            }
            if (options.alignTopZval) {
                if (compareNames([options.category], ["星盆", "炉灶"])) {
                    let bbox_rect = ZRect.fromBox3(box, { x: 0, y: 1, z: 0 });
                    bbox_rect.zval = 0;

                    if (!group_node.userData[UserDataKey.MainChildBBox]) {
                        let min_diff = 10000;
                        let target_bbox: Box3 = null;
                        let target_mesh: Mesh = null;
                        group_node.traverseVisible((obj) => {
                            let mesh = obj as Mesh;
                            if (mesh.isMesh) {
                                let child_bbox = new Box3();
                                child_bbox.setFromObject(obj);

                                let tt_rect = ZRect.fromBox3(child_bbox, { x: 0, y: 1, z: 0 });
                                let child_height = child_bbox.max.z - child_bbox.min.z;
                                let diff = Math.abs(tt_rect.w - bbox_rect.w) + Math.abs(tt_rect.h - bbox_rect.h);
                                if (!target_bbox || diff < min_diff) {
                                    target_bbox = child_bbox;
                                    target_mesh = mesh;
                                    min_diff = diff;
                                }
                            }
                        });

                        if (target_mesh) {
                            let box1 = Model3dApi.computeBoundaryRectsOfMesh(target_mesh);
                            group_node.userData[UserDataKey.MainChildBBox] = box1;
                        }
                    }
                    let target_bbox: Box3 = group_node.userData[UserDataKey.MainChildBBox];
                    if (target_bbox) {
                        let z_hh = (target_bbox.max.z - box.min.z);
                        rect.zval = options.alignTopZval - z_hh;
                    }
                }
                else {
                    rect.zval = options.alignTopZval - t_hh;
                }
            }
            let pos = (rect.rect_center_3d.clone().add({ x: 0, y: 0, z: t_hh / 2 }));
            matrix.multiply(new Matrix4().makeTranslation(pos));
            matrix.multiply(new Matrix4().makeRotationZ(rotation_z));
            matrix.multiply(new Matrix4().makeScale(target_size.length / box_length, target_size.width / box_width, t_hh / box_height));
            matrix.multiply(new Matrix4().makeTranslation(center.clone().negate()));
            group_node.position.set(0, 0, 0);
            group_node.rotation.set(0, 0, 0);
            group_node.scale.set(1, 1, 1);
            group_node.updateMatrix();
            group_node.applyMatrix4(matrix);
            group_node.updateMatrix();
        }
        else {
            let cabinet_data = group_node.userData.cabient_data;

            let pos = rect.rect_center_3d.clone();
            let m_ll = target_size.length;
            let m_dd = target_size.width;
            let m_hh = target_size.height;
            let box3 = SvgGltfNode.UpdateBox3(group_node);

            if(category.includes("地柜"))
            {
                pos.z = 0;
            }

            if(category.includes("吊柜"))
            {
                pos.z = 2400 - m_hh;
            }

            group_node.position.copy(pos);

            group_node.rotation.set(0, 0, rect.rotation_z);

            group_node.scale.set(1, 1, 1);


            if (cabinet_data) {
                if(group_node instanceof SvgGltfCabinetNode)
                {
                    if(category.startsWith("地柜"))
                    {
                        GltfManager.updateSpacePoseOfCabinet(group_node,{W:rect.w});
                    }
                    else{
                        GltfManager.updateSpacePoseOfCabinet(group_node,{W:rect.w, H:m_hh});

                    }
        

                }
                else
                {
                    group_node.scale.set(m_ll / cabinet_data.length,m_dd / cabinet_data.width, m_hh / (cabinet_data.height || m_hh));
                }                
            }

            if (!Model3dApi.isGlbCabinet) {
                // Model3dApi.UpdateCabinetBoardMaterial(group_node, options);
            }
        }
    }
    static async MakeMesh3D_WithA3dSource(materialId: string, A3dSource: string, isGlb: boolean, isWhite: boolean) {
        if (A3dSource) {
            if (A3dSource.includes(".svj") || A3dSource.includes(".a3d") || A3dSource.includes(".A3D")) {
                let group_node: Group = null;
                if (!group_node) {
                    let wholeUrl = getImgDomain() + "/" + A3dSource + `&id=${materialId}`;
                    if (isGlb) {
                        A3dSource = A3dSource.replace("//", "/");
                        if (!A3dSource.startsWith("/")) {
                            A3dSource = "/" + A3dSource;
                        }
                        wholeUrl = "https://3vj-designmaterial.3vjia.com" + A3dSource;
                        group_node = await SimpleGlbParser.loadGLBParser(wholeUrl, isWhite);
                    }
                    else {
                        group_node = await SimpleSVJParser.loadSVJParser(wholeUrl, isWhite);
                    }
                }

                if (group_node) {
                    group_node.userData[UserDataKey.FigureMeshTypeName] = MeshName.SolidMaterial;
                    group_node.userData[UserDataKey.MaterialId] = materialId;
                    group_node.userData[UserDataKey.IsModel] = materialId;

                    group_node.traverse((object) => {
                        if ((object as Mesh).isMesh) {
                            let mesh = object as Mesh;

                            if (mesh.material) {
                                MeshBuilder.bindMeshMaterials(mesh);
                            }
                        }
                    });
                } else {
                    console.warn("model is null", materialId, A3dSource);
                }
                return group_node;
            } else {
                console.warn("unknown a3d model type", materialId, A3dSource);
            }
        }
        return null;
    }

    static setGroupWhiteStandardMaterial(group: Group) {
        group.traverse((object) => {
            if ((object as Mesh).isMesh) {
                let mesh = object as Mesh;
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach((material) => {
                        if (material instanceof MeshStandardMaterial) {
                            Model3dApi.setWhiteStandardMaterial(material);
                        }
                    });
                }
                else {
                    if (mesh.material instanceof MeshStandardMaterial) {
                        Model3dApi.setWhiteStandardMaterial(mesh.material);
                    }
                }
            }
        });
    }

    static setWhiteStandardMaterial(material: MeshStandardMaterial) {
        material.map = null;
        material.color.setRGB(1, 1, 1);
        material.emissive.setRGB(1, 1, 1);
        material.emissiveMap = null;
        material.needsUpdate = true;
    }

    static async MakeMesh3D_WithContentUrl(materialId: string, contentUrl: string, category: string, onLoadEnd: (groupNode: Group) => void, isWhite: boolean) {
        let xml_data = await get_swj_xml_from_url(getImgDomain() + contentUrl);
        if (!xml_data) {
            console.warn("xml_data is null", materialId, contentUrl);
            return null;
        }
        const parser = new DOMParser();

        let id = xml_data.indexOf("<");
        if (id >= 0) {
            if (xml_data[id + 1] == '<') {
                id = id + 1;
            }
        }
        xml_data = xml_data.substring(id);
        let doc = parser.parseFromString(xml_data, "application/xml");
        let cabinet_data = (DesignXmlParser.instance).parseCabinet(doc.children[0]);

        let node3d: Group = null;
        if (cabinet_data._is_parts_cabinet === true) {
            node3d = await Cabinet3DApi.computeMesh3DFromSwjCupboardPart(cabinet_data  as any, null, category, isWhite);
        }
        else {
            node3d = await Cabinet3DApi.computeMesh3DFromSwjCabinet(cabinet_data as any, null, 0, 3, isWhite);
            node3d.updateMatrixWorld(true);

            let sub_meshes: Mesh[] = [];
            const visit_node = (node: Object3D, parent_matrix: Matrix4 = null) => {
                if (node.userData.type && node.userData.type.includes("Furniture") && node.type === "Mesh") {
                    sub_meshes.push(node as Mesh);
                }

                if (node.type === "Mesh") {
                    if (node.userData.type && !node.userData.type.includes("swingDoor")) return;
                    sub_meshes.push(node as Mesh);
                }
                if (node.children) {
                    node.children.forEach(ele => {
                        visit_node(ele);
                    })
                }
            }
            visit_node(node3d);

            const visit_door_data = (data: I_SwjCabinetData, leaf_list: I_SwjCabinetData[], parent_matrix: Matrix4 = null) => {
                if (data.material_id && !data._children) {
                    data._parent_matrix = parent_matrix;
                    leaf_list.push(data);
                }
                if (data._children) {
                    let matrix = Cabinet3DApi.computeCabinetBoardMatrix(data as any, parent_matrix);
                    data._children.forEach((ele) => visit_door_data(ele, leaf_list, matrix));
                }
            }

            let replacing_solid_list: { target_mesh: Mesh, sub_data: I_SwjCabinetData }[] = [];
            sub_meshes.forEach((door_mesh) => {
                let cabinet_data = door_mesh.userData.cabinet_data;
                if (!cabinet_data) return;
                let sub_data_list: I_SwjCabinetData[] = [];
                visit_door_data(cabinet_data, sub_data_list);
                sub_data_list.forEach(async (sub_data) => {
                    // if(sub_data.type as any=== "door_board") return;
                    if (sub_data.type as any === "buckle") return;
                    replacing_solid_list.push({ target_mesh: door_mesh, sub_data: sub_data });
                });
            });
            let done_replacing_list: { target_mesh: Mesh, sub_data: I_SwjCabinetData }[] = [];
            const DoneReplacing = (data: any) => {
                done_replacing_list.push(data);
                if (done_replacing_list.length == replacing_solid_list.length) {
                    if (onLoadEnd) {
                        onLoadEnd(node3d);
                    }
                }
            }

            replacing_solid_list.forEach(async (data) => {
                let data_node = await Cabinet3DApi.computeMesh3DFromSwjCabinet(data.sub_data as any, data.sub_data._parent_matrix, 0, 0, isWhite);
                if (data_node && data_node.children && data_node.children.length > 0) {
                    data.target_mesh.add(data_node);
                    data.target_mesh.scale.set(1, 1, 1);
                }
                DoneReplacing(data);
            });
        }

        return node3d;
    }

    // PICLength、PICWidth、PICHeight 素材配置的尺寸
    static async MakeMesh3DWithGroupDesignMaterialInfo(design_material_info: I_DesignMaterialInfo, options: MaterialInfoOptions = {}) {
        let memberMaterials: I_MaterialMatchingItem[] = await MaterialService.getGroupMaterialDetail(design_material_info.MaterialId);
        if (memberMaterials.length == 0) {
            console.error("memberMaterials is empty", design_material_info.MaterialId);
            return null;
        }

        let materialIDs: string[] = memberMaterials.map(e => e.modelId);

        let resList = await MaterialService.getDesignMaterialInfoByIds(materialIDs);
        let designInfoList = materialIDs.map(id => resList.find(e => e.MaterialId === id));

        let groupNode = new Group();
        let isWhite = !!options.isWhite;

        // 解析组合素材的子素材
        let promiseList: Promise<Group>[] = [];
        for (const itemDvo of designInfoList) {
            if (isGroupDesignMaterialInfo(itemDvo)) {
                promiseList.push(Model3dApi.MakeMesh3DWithGroupDesignMaterialInfo(itemDvo, { isWhite: isWhite, isSubModel: true }));
            }
            else {
                promiseList.push(Model3dApi.MakeMesh3DWithDesignMaterialInfo(itemDvo, { isWhite: isWhite, isSubModel: true }));
            }
        }

        let itemNodes = await Promise.all(promiseList);

        if (itemNodes.length != designInfoList.length) {
            console.error("itemNodes.length != designInfoList.length", itemNodes.length, designInfoList.length);
        }

        for (let itemNode of itemNodes) {
            if (!itemNode) {
                console.error("itemNode is null", designInfoList);
                continue;
            }

            let itemMaterialID = itemNode.userData[UserDataKey.MaterialId];
            let itemMaterial = memberMaterials.find(e => e.modelId === itemMaterialID);
            itemNode.scale.set(
                itemMaterial.targetSize.length / itemMaterial.originalLength,
                itemMaterial.targetSize.width / itemMaterial.originalWidth,
                itemMaterial.targetSize.height / itemMaterial.originalHeight
            );
            itemNode.position.set(itemMaterial.targetPosition.x, itemMaterial.targetPosition.y, itemMaterial.targetPosition.z);
            itemNode.rotation.set(itemMaterial.targetRotation.x, itemMaterial.targetRotation.y, itemMaterial.targetRotation.z);
            itemNode.userData[UserDataKey.MaterialId] = itemMaterial.modelId;

            groupNode.add(itemNode);
        }

        // 组合素材节点的属性
        groupNode.userData[UserDataKey.FigureMeshTypeName] = MeshName.SolidMaterial;
        let target_size = options.target_size || { length: 1000, width: 1000, height: 1000 };
        const getPICValue = (val: string | number, default_val: number) => {
            if (!val) return default_val;
            let n_val = parseFloat('' + val);
            if (Math.abs(n_val) > 0.1) return n_val;
            return default_val;
        }
        let m_ll = target_size.length;
        let m_dd = target_size.width;
        let m_hh = target_size.height;

        m_ll = getPICValue(design_material_info?.PICLength, m_ll);
        m_dd = getPICValue(design_material_info?.PICWidth, m_dd);
        m_hh = getPICValue(design_material_info?.PICHeight, m_hh);
        let target_rect = options.target_rect || new ZRect(m_ll, m_dd);
        let rect = target_rect;
        groupNode.position.copy(rect.rect_center_3d);

        let hh = target_size.height;
        // 获取模型源文件的尺寸  因为存在模型源文件尺寸和模型信息尺寸不一致的情况
        // 先缩放再旋转 防止旋转之后获取的源文件尺寸长宽互换了
        let box = new Box3().setFromObject(groupNode);
        let box_length = box.max.x - box.min.x;
        let box_width = box.max.y - box.min.y;
        let box_height = box.max.z - box.min.z;
        groupNode.scale.set(rect.w / box_length, rect.h / box_width, hh / box_height);
        groupNode.rotation.set(0, 0, rect.rotation_z);

        if (!options.isSubModel) {
            let t_mesh = new Mesh(GeometryBuilder.box_geometry, MaterialManager.white_box_material);
            t_mesh.name = UserDataKey.ProxyBox;
            t_mesh.visible = false;
            t_mesh.scale.set(rect.w, rect.h, hh);
            t_mesh.rotation.copy(groupNode.rotation);
            t_mesh.position.copy(groupNode.position).setZ(groupNode.position.z + hh / 2);
            groupNode.userData[UserDataKey.ProxyBox] = t_mesh;
            groupNode.userData[UserDataKey.FurnitureType] = MeshName.GroupModel;
        }

        return groupNode;
    }


    static computeBoundaryRectsOfMesh(mesh: Mesh) {
        let geometry = mesh.geometry;

        if (!geometry.boundingBox) {
            geometry.computeBoundingBox();
        }
        let boundingbox = geometry.boundingBox;
        let m_rect = ZRect.fromBox3(boundingbox);


        let postions = geometry.attributes.position as Float32BufferAttribute;

        let booundary_pos_list: Vector3[] = [];
        for (let i = 0; i < postions.count; i++) {
            let x = postions.getX(i);
            let y = postions.getY(i);
            let z = postions.getZ(i);

            let pos = new Vector3(x, y, z);
            let dist = m_rect.distanceToPoint(pos.clone().setZ(0));

            if (Math.abs(dist) < 30) {
                booundary_pos_list.push(pos);
            }
        }
        let box1 = new Box3().setFromPoints(booundary_pos_list);

        box1.applyMatrix4(mesh.matrix);
        return box1;

    }
}
