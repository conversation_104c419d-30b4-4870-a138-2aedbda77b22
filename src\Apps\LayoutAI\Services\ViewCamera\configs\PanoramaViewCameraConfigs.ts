import { ViewCameraRuler, ViewCameraRulerType, ViewCameraType } from "../ViewCameraRuler"
import { CategoryName } from "@/Apps/LayoutAI/Scene3D/NodeName"

// 全景视角规则配置
export const PanoramaViewCameraConfigs: ViewCameraRuler[] = [
    {
        name: '客厅-朝向沙发正面',
        typeId: ViewCameraRulerType.SofaView,
        target: CategoryName.Sofa,
        viewCamera: {
            type: ViewCameraType.Panorama,
            ukey: 'sofa_view_camera',
            fov: 60,
        },
        pose: {
            z: 1200,
        },
        condition: {
            spaceArea: '客厅区',
        },
    },
]