import { LayoutAI_App } from "@/Apps/LayoutAI_App";
import { ZEdge, ZPolygon, ZRect } from "@layoutai/z_polygon";
import { Vector3 } from "three";
import { I_Window } from "../../../IRoomInterface";
import { TFigureElement } from "../../../TFigureElements/TFigureElement";
import { TBaseGroupEntity } from "../../../TLayoutEntities/TBaseGroupEntity";
import { TGroupTemplate } from "../../../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TRoom } from "../../../TRoom";
import { I_LayoutScore } from "../../TLayoutJudge";

export enum OverlayType {
    k_noOverlay = 0,
    k_overlay,
}

export enum RectEdgeType {
    k_back = 1,
    k_front,
    k_left,
    k_right
}

export enum DoorType
{
    k_none,
    k_balcony,
    k_kitchen,
    k_bedRoom,
    k_bathRoom,
}
export interface I_Range2D
{
    xMin : number, yMin:number, xMax:number,yMax:number
}

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export class TBaseRoomToolUtil {
    protected static _instance: TBaseRoomToolUtil = null;

    public tol: number = 0.001;

    protected constructor() {
    }

    public static get instance(): TBaseRoomToolUtil {
        if (TBaseRoomToolUtil._instance == null) {
            TBaseRoomToolUtil._instance = new TBaseRoomToolUtil();
        }
        return TBaseRoomToolUtil._instance;
    }

    // 一字形状<方向向量, 矩形>
    public getIShapes(figures: TFigureElement[]): TFigureElement[][] {
        return getAllIShape(figures);
    }

    public sortFigureElementsByCenter(figures: TFigureElement[]) {
        if (!figures.length) {
            return;
        }
        let xMin: number = figures[0].rect.rect_center.x;
        let xMax: number = figures[0].rect.rect_center.x;
        let yMin: number = figures[0].rect.rect_center.y;
        let yMax: number = figures[0].rect.rect_center.y;

        figures.forEach(figure => {
            xMin = xMin > figure.rect.rect_center.x ? figure.rect.rect_center.x : xMin;
            xMax = xMax < figure.rect.rect_center.x ? figure.rect.rect_center.x : xMax;
            yMin = yMin > figure.rect.rect_center.y ? figure.rect.rect_center.y : yMin;
            yMax = yMax < figure.rect.rect_center.y ? figure.rect.rect_center.y : yMax;
        });
        let xRange: number = xMax - xMin;
        let YRange: number = yMax - yMin;
        let firstRect: ZRect = getFirstRect(figures, xRange > YRange);

        figures.sort((figure1: TFigureElement, figure2: TFigureElement) =>
            firstRect.rect_center.sub(figure2.rect.rect_center).length() - firstRect.rect_center.sub(figure1.rect.rect_center).length()
        );
    }

    public isLayonLeftOrRight(rect1: ZRect, rect2: ZRect): boolean {
        if (rect1.leftEdge.islayOn(rect2.rightEdge) || rect1.rightEdge.islayOn(rect2.leftEdge)) {
            return true;
        }
        return false;
    }

    public isLayOnPolygons(rect1: ZPolygon, rect2: ZPolygon, lResult: { layon_len?: number, ll?: number, rr?: number }, ratio: number = 0.1, tol: number = 5): any {
        for (let edge1 of rect1.edges) {
            for (let edge2 of rect2.edges) {
                if (edge1.islayOn(edge2, tol, ratio, lResult)) {
                    return { edge1: edge1, edge2: edge2 };
                }
            }
        }
        return null;
    }

    public getLayonRoomEdges(poly1: ZPolygon, poly2: ZPolygon, lResult: { layon_len?: number, ll?: number, rr?: number }, ratio: number = 0.1, tol: number = 5): ZEdge[]
    {
        let layonRoomEdges: ZEdge[] = [];
        for(let edge1 of poly1.edges)
        {
            for (let edge2 of poly2.edges) {
                if (edge1.islayOn(edge2, tol, ratio, lResult)) {
                    layonRoomEdges.push(edge1);
                }
            }
        }
        return layonRoomEdges;
    }

    public calDirByVectors(points: Vector3[]): Vector3 {
        if (points.length < 2) {
            return null;
        }
        let basePoint: Vector3 = points[0];
        let dir: Vector3 = new Vector3(0, 0, 0);
        for (let i = 1; i < points.length; i++) {
            dir.add(basePoint.clone().sub(points[i]));
        }
        dir.normalize();
        return dir;
    }

    public calEdgePair(polygon: ZPolygon): ZEdge[][] {
        return calEdgePair(polygon);
    }

    public calDistance(edge1: ZEdge, edge2: ZEdge): number {
        return Math.min(Math.abs(subVector(edge1.v0.pos, edge2.v0.pos).dot(edge1.nor)),
            Math.abs(subVector(edge1.v0.pos, edge2.v1.pos).dot(edge1.nor)));
    }

    public calDistanceBetweenEdgeAndPolygon(edge: ZEdge, polygon: ZPolygon, tol: number = 0.01, isNear: boolean = true): number {
        if (isNear) {
            let minDist: number = Number.POSITIVE_INFINITY;
            for (let polygonEdge of polygon.edges) {
                let dist: number = this.calDistance(edge, polygonEdge);
                if (dist < minDist && 1 - Math.abs(edge.dv.dot(polygonEdge.dv)) < tol) {
                    minDist = dist;
                }
            }
            return minDist;
        }
        else {
            let maxDist: number = Number.NEGATIVE_INFINITY;
            for (let polygonEdge of polygon.edges) {
                let dist: number = this.calDistance(edge, polygonEdge);
                if (dist > maxDist && 1 - Math.abs(edge.dv.dot(polygonEdge.dv)) < tol) {
                    maxDist = dist;
                }
            }
            return maxDist;
        }
    }

    public findNearLength(lengths: number[], dis: number, tol: number = 0.01): boolean {
        return findNearLength(lengths, dis, tol);
    }

    public calDistanceByPolygons(poly1: ZPolygon, poly2: ZPolygon, isContain: boolean = false): number {
        // 检查poly2是否在poly1某条边的前方
        let minDis: number = Number.POSITIVE_INFINITY;
        if (isContain) {
            for (let edge1 of poly1.edges) {
                for (let edge2 of poly2.edges) {
                    if (Math.abs(edge1.dv.clone().dot(edge2.dv)) < 0.9) {
                        continue;
                    }
                    let subVec: Vector3 = subVector(edge1.v0.pos, edge2.v0.pos);
                    let dis: number = Math.abs(subVec.dot(edge2.nor));
                    if (dis < minDis) {
                        minDis = dis;
                    }
                }
            }
        }
        else {
            let edgePairs: ZEdge[][] = this.calEdgePair(poly1);
            for (let edgePair of edgePairs) {
                if (this.polygonIsInFrontArea(edgePair[0], edgePair[1], poly2)) {
                    let dis = this.calDistanceBetweenEdgeAndPolygon(edgePair[0], poly2);
                    return dis;
                }
            }

            for (let vertex1 of poly1.vertices) {
                for (let vertex2 of poly2.vertices) {
                    let dis: number = vertex1.pos.distanceTo(vertex2.pos);
                    if (minDis > dis) {
                        minDis = dis;
                    }
                }
            }
        }
        return minDis;
    }

    public isOverlayByFigures(figure1: TFigureElement, figure2: TFigureElement, ignoreHeight: boolean = false): boolean {
        let box1: any = getBoundingBox(figure1);
        let box2: any = getBoundingBox(figure2);

        let isOverlay: boolean = box1.xMax > box2.xMin && box1.xMin < box2.xMax && box1.yMax > box2.yMin && box1.yMin < box2.yMax && (ignoreHeight ? true : box1.zMax > box2.zMin && box1.zMin < box2.zMax);

        if (isOverlay) {
            let intersectPolys: ZPolygon[] = figure1.rect.intersect_polygons([figure2.rect]);
            if (intersectPolys.length == 0) {
                isOverlay = false;
            }
        }
        return isOverlay;
    }


    public isOverlayByRects(rect1: ZRect, rect2: ZRect, ignoreHeight: boolean = true): boolean {
        if(!rect1 || !rect2)
        {
            return false;
        }

        let box1: any = this.getRange3dByPolygon(rect1);
        let box2: any = this.getRange3dByPolygon(rect2);

        let isOverlay: boolean = box1.xMax > box2.xMin && box1.xMin < box2.xMax && box1.yMax > box2.yMin && box1.yMin < box2.yMax && (ignoreHeight ? true : box1.zMax > box2.zMin && box1.zMin < box2.zMax);

        if (isOverlay) {
            let intersectPolys: ZPolygon[] = rect1.intersect_polygons([rect2]);
            if (intersectPolys.length == 0) {
                isOverlay = false;
            }
        }
        return isOverlay;
    }

    public getRange3dByPolygon(polygon: ZPolygon): any {
        let xMin: number = Number.POSITIVE_INFINITY;
        let xMax: number = Number.NEGATIVE_INFINITY;
        let yMin: number = Number.POSITIVE_INFINITY;
        let yMax: number = Number.NEGATIVE_INFINITY;
        let zMin: number = Number.POSITIVE_INFINITY;
        let zMax: number = Number.NEGATIVE_INFINITY;

        for (let vertex of polygon.vertices) {
            if (xMin > vertex.pos.x) {
                xMin = vertex.pos.x;
            }
            if (xMax < vertex.pos.x) {
                xMax = vertex.pos.x;
            }
            if (yMin > vertex.pos.y) {
                yMin = vertex.pos.y;
            }
            if (yMax < vertex.pos.y) {
                yMax = vertex.pos.y;
            }
            if (zMin > vertex.pos.z) {
                zMin = vertex.pos.z;
            }
            if (zMax < vertex.pos.z) {
                zMax = vertex.pos.z;
            }
        }
        return { xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax, zMin: zMin, zMax: zMax };
        
    }

    public isContainByFigures(baseFigure: TFigureElement, otherFigure: TFigureElement, ignoreHeight: boolean = false, tol: number = 2, isMatchedRect: boolean = false): boolean {
        let baseBox: any = getBoundingBox(baseFigure, isMatchedRect);
        let otherBox: any = getBoundingBox(otherFigure, isMatchedRect);

        return (baseBox.xMin - tol) <= otherBox.xMin && baseBox.xMax >= (otherBox.xMax - tol) &&
            (baseBox.yMin - tol) <= otherBox.yMin && baseBox.yMax >= (otherBox.yMax - tol) &&
            (ignoreHeight ? true : baseBox.zMin <= otherBox.yMin && baseBox.zMax >= otherBox.zMax);
    }

    public findEdgeContainInRect(targetRect: ZRect, sourceRect: ZRect): ZEdge {
        let box2 = this.getRange2dByPolygon(sourceRect);
        for (let edge of targetRect.edges) {
            let xMin = Math.min(edge.v0.pos.x, edge.v1.pos.x);
            let xMax = Math.max(edge.v0.pos.x, edge.v1.pos.x);
            let yMin = Math.min(edge.v0.pos.y, edge.v1.pos.y);
            let yMax = Math.max(edge.v0.pos.y, edge.v1.pos.y);
            if ((xMin > box2.xMin && xMax < box2.xMax) || (yMin > box2.yMin && yMax < box2.yMax)) {
                return edge;
            }
        }
        return null;
    }

    public getRectsByFigures(figures: TFigureElement[], isMatchedRect: boolean = false): ZRect[] {
        let rects: ZRect[] = [];
        figures.forEach((figure) => {
            let rect = isMatchedRect ? figure?.matched_rect || figure?.rect : figure?.rect;
            if (rect) {
                rects.push(rect.clone());
            }
        });
        return rects;
    }

    public getOtherShapeRects(currentFigure: TFigureElement, figures: TFigureElement[]): ZRect[] {
        let otherFigures: TFigureElement[] = this.getOtherFigures(currentFigure, figures);
        let otherRects: ZRect[] = TBaseRoomToolUtil.instance.getRectsByFigures(otherFigures);
        return otherRects;
    }

    // edge1是基础边，edge2是辅助边, edge1与edge2是对边
    public polygonIsInFrontArea(edge1: ZEdge, edge2: ZEdge, polygon: ZPolygon, cutRatio: number = 0): boolean {
        let cutEdge: ZEdge = edge1;
        if (cutRatio) {
            let edgedv1: Vector3 = edge1.dv;
            let cutLen: number = edge1.length * cutRatio;
            let cutEdgeStart: Vector3 = edge1.v0.pos.clone().add(edgedv1.clone().multiplyScalar(cutLen));
            let cutEdgeEnd: Vector3 = edge1.v1.pos.clone().add(edgedv1.clone().multiplyScalar(-cutLen));
            cutEdge = new ZEdge({ pos: cutEdgeStart }, { pos: cutEdgeEnd });
            cutEdge.computeNormal();
        }
        let isInFront: boolean = false;
        if (this.isInRangeByEdgeForPolygon(cutEdge, polygon)) {
            // TODO 在范围内后，还需要判断是否在前方
            if (isInFrontAreaForPolygon(edge1, edge2, polygon)) {
                isInFront = true;
            }
        }
        return isInFront;
    }

    public isInRangeByEdgeForPolygon(baseEdge: ZEdge, polygon: ZPolygon, cutPolygonRation: number = 0): boolean {
        let baseXMin: number = Math.min(baseEdge.v0.pos.x, baseEdge.v1.pos.x);
        let baseXMax: number = Math.max(baseEdge.v0.pos.x, baseEdge.v1.pos.x);
        let baseYMin: number = Math.min(baseEdge.v0.pos.y, baseEdge.v1.pos.y);
        let baseYMax: number = Math.max(baseEdge.v0.pos.y, baseEdge.v1.pos.y);

        let polygonXMin: number = Number.POSITIVE_INFINITY;
        let polygonXMax: number = Number.NEGATIVE_INFINITY;
        let polygonYMin: number = Number.POSITIVE_INFINITY;
        let polygonYMax: number = Number.NEGATIVE_INFINITY;
        polygon.edges.forEach(edge => {
            let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
            let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
            let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
            let yMax: number = Math.max(edge.v0.pos.y, edge.v0.pos.y);

            if (polygonXMin > xMin) {
                polygonXMin = xMin;
            }

            if (polygonXMax < xMax) {
                polygonXMax = xMax;
            }

            if (polygonYMin > yMin) {
                polygonYMin = yMin;
            }

            if (polygonYMax < yMax) {
                polygonYMax = yMax;
            }
        });
        // polygon 整体进行内缩
        let xCutLen: number = (polygonXMax - polygonXMin) * cutPolygonRation;
        let yCutLen: number = (polygonYMax - polygonYMin) * cutPolygonRation;
        polygonXMin += xCutLen;
        polygonXMax -= xCutLen;
        polygonYMin += yCutLen;
        polygonYMax -= yCutLen;
        return (baseXMax > polygonXMin && baseXMin < polygonXMax) || (baseYMax > polygonYMin && baseYMin < polygonYMax);
    }

    public edgeOnPolygon(edge: ZEdge, polygon: ZPolygon, tol: number = 5, ratio: number = 0.9): ZEdge {
        for (let polygonEdge of polygon.edges) {
            if (polygonEdge.islayOn(edge, tol, ratio)) {
                return polygonEdge;
            }
        }
        return null;
    }

    private isEdgesMixByAxis(edge: ZEdge, otherEdge: ZEdge, cutRatio: number = 0, isEqual: boolean = false, tol: number = 1): boolean
    {
        let startPoint: Vector3 = edge.v0.pos.clone().add(edge.dv.clone().multiplyScalar(edge.length * cutRatio));
        let endPoint: Vector3 = edge.v1.pos.clone().sub(edge.dv.clone().multiplyScalar(edge.length * cutRatio));
        let baseXMin: number = Math.min(startPoint.x, endPoint.x);
        let baseXMax: number = Math.max(startPoint.x, endPoint.x);
        let baseYMin: number = Math.min(startPoint.y, endPoint.y);
        let baseYMax: number = Math.max(startPoint.y, endPoint.y);

        let otherXMin: number = Math.min(otherEdge.v0.pos.x, otherEdge.v1.pos.x);
        let otherXMax: number = Math.max(otherEdge.v0.pos.x, otherEdge.v1.pos.x);
        let otherYMin: number = Math.min(otherEdge.v0.pos.y, otherEdge.v1.pos.y);
        let otherYMax: number = Math.max(otherEdge.v0.pos.y, otherEdge.v1.pos.y);

        let flag = isEqual ? ((baseXMax >= otherXMin && baseXMin <= otherXMax) || (baseYMax >= otherYMin && baseYMin <= otherYMax)) :
                    ((baseXMax > otherXMin && baseXMin < otherXMax) || (baseYMax > otherYMin && baseYMin < otherYMax));
        if(flag)
        {
            let commonLen: number = Number.POSITIVE_INFINITY;
            if(baseXMax >= otherXMin && baseXMin <= otherXMax)
            {
                commonLen = Math.min(baseXMax - otherXMin, otherXMax - baseXMin);
            }
            else if(baseYMax >= otherYMin && baseYMin <= otherYMax)
            {
                commonLen = Math.min(baseYMax - otherYMin, otherYMax - baseYMin);
            }
            if(commonLen < tol)
            {
                flag = false
            }
        }
        return flag;
    }

    public edgeIsInFrontArea(edge1: ZEdge, edge2: ZEdge, otherEdge: ZEdge, cutRatio: number = 0, isEqual: boolean = false, tol: number = 1): boolean {
        let flag: boolean = this.isEdgesMixByAxis(edge1, otherEdge, cutRatio, isEqual, tol);
        if (flag) {
            // TODO 在范围内后，还需要判断是否在前方
            if (isInFrontAreaForEdge(edge1, edge2, otherEdge)) {
                return true;
            }
        }
        return false;
    }

    public edgeInFrontEdgeDir(edege: ZEdge, nor: Vector3, otherEdge: ZEdge, cutRatio: number = 0, isEqual: boolean = false, tol: number = 1): boolean
    {
        let flag: boolean = this.isEdgesMixByAxis(edege, otherEdge, cutRatio, isEqual, tol);
        if(flag)
        {
            if(isInFrontEdgeDirForEdge(edege, nor, otherEdge))
            {
                return true;
            }
        }
        return false;
    }

    public isCrossByVertex(edge1V0: Vector3, edge1V1: Vector3, edge2V0: Vector3, edge2V1: Vector3, tol: number = 0.1): boolean {
        let subVec: Vector3 = subVector(edge1V1, edge1V0);
        let vec1: Vector3 = subVector(edge2V0, edge1V0);
        let vec2: Vector3 = subVector(edge2V1, edge1V0);

        let flag: number = subVec.clone().cross(vec1).dot(subVec.clone().cross(vec2));
        if (flag < 0) {
            let edge1: ZEdge = new ZEdge({ pos: edge1V0 }, { pos: edge1V1 });
            let edge2: ZEdge = new ZEdge({ pos: edge2V0 }, { pos: edge2V1 });
            edge1.computeNormal();
            edge2.computeNormal();
            let result = edge1.checkIntersection(edge2, tol);
            if (result) {
                return true;
            }
        }
        return false;
    }

    public getRange2dByPolygon(polygon: ZPolygon) :I_Range2D {
        let xMin: number = Number.POSITIVE_INFINITY;
        let xMax: number = Number.NEGATIVE_INFINITY;
        let yMin: number = Number.POSITIVE_INFINITY;
        let yMax: number = Number.NEGATIVE_INFINITY;
        for (let vertex of polygon.vertices) {
            if (xMin > vertex.pos.x) {
                xMin = vertex.pos.x;
            }
            if (xMax < vertex.pos.x) {
                xMax = vertex.pos.x;
            }
            if (yMin > vertex.pos.y) {
                yMin = vertex.pos.y;
            }
            if (yMax < vertex.pos.y) {
                yMax = vertex.pos.y;
            }
        }
        return { xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax };
    }

    public getRange2dByEdge(edge: ZEdge): any
    {
        let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
        let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
        let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
        let yMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);
        return {xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax};
    }

    public isContainByRange(baseRange: I_Range2D, range: I_Range2D, isEqual: boolean = true): boolean {
        if(isEqual)
        {
            return baseRange.xMin <= range.xMin && baseRange.xMax >= range.xMax && baseRange.yMin <= range.yMin && baseRange.yMax >= range.yMax;
        }
        else
        {
            return baseRange.xMin < range.xMin && baseRange.xMax > range.xMax && baseRange.yMin < range.yMin && baseRange.yMax > range.yMax;
        }
    }

    public calMinFlowDistanceByPolygons(sourcePolygons: ZPolygon[]): any {
        let dirFlag: Vector3 = sourcePolygons[0].edges[0].dv.clone().cross(sourcePolygons[0].edges[1].dv);
        let sameDirPolys: ZPolygon[] = sourcePolygons.filter(poly =>
            dirFlag.dot(poly.edges[0].dv.cross(poly.edges[1].dv)) > 0
        );
        let diffSameDirPolys: Map<ZPolygon, ZPolygon[]> = new Map<ZPolygon, ZPolygon[]>();
        let innerDiffSameDirPolys: ZPolygon[] = sourcePolygons.filter(poly => dirFlag.dot(poly.edges[0].dv.cross(poly.edges[1].dv)) < 0);
        let commonEdgePairLen: number = null;
        // 外轮廓多边形
        for (let outPoly of sameDirPolys) {
            let innerPolys: ZPolygon[] = [];
            // 从里面进行筛选
            let sameRange = TBaseRoomToolUtil.instance.getRange2dByPolygon(outPoly);
            for (let innerPoly of innerDiffSameDirPolys) {
                let innerRange = TBaseRoomToolUtil.instance.getRange2dByPolygon(innerPoly);
                if (TBaseRoomToolUtil.instance.isContainByRange(sameRange, innerRange)) {
                    innerPolys.push(innerPoly);
                }
            }

            if (innerPolys.length) {
                diffSameDirPolys.set(outPoly, innerPolys);
            }
        }
        let allMinLen: number = Number.POSITIVE_INFINITY;
        if (diffSameDirPolys?.size > 0) {
            let minDis: number = Number.POSITIVE_INFINITY;
            for (let i = 0; i < sameDirPolys.length; ++i) {
                let innerPolys: ZPolygon[] = diffSameDirPolys.get(sameDirPolys[i]);
                for (let j = 0; j < innerPolys?.length; ++j) {
                    let dis: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(sameDirPolys[i], innerPolys[j], true);
                    if (dis < minDis) {
                        minDis = dis;
                    }
                }
            }

            for (let innerPolys of diffSameDirPolys.values()) {
                for (let i = 0; i < innerPolys.length; ++i) {
                    for (let j = i + 1; j < innerPolys.length; ++j) {
                        let dis: number = TBaseRoomToolUtil.instance.calDistanceByPolygons(innerPolys[i], innerPolys[j]);
                        if (dis < minDis) {
                            minDis = dis;
                        }
                    }
                }
            }

            if (minDis > 10) {
                if (allMinLen > minDis) {
                    allMinLen = minDis;
                }
            }
        }

        for (let polygon of sameDirPolys) {
            let edgePairs: ZEdge[][] = TBaseRoomToolUtil.instance.calEdgePair(polygon);
            // 下方是临时写的
            let newEdgePair: ZEdge[][] = [];
            let edgeLengths: number[] = [];

            let xMin: number = Number.POSITIVE_INFINITY;
            let xMax: number = Number.NEGATIVE_INFINITY;
            let yMin: number = Number.POSITIVE_INFINITY;
            let yMax: number = Number.NEGATIVE_INFINITY;
            polygon.edges.forEach(edge => {
                edgeLengths.push(edge.length);
                let tempXMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
                let tempXMax: number = Math.max(edge.v1.pos.x, edge.v1.pos.x);
                let tempYMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
                let tempYMax: number = Math.max(edge.v1.pos.y, edge.v1.pos.y);
                if (tempXMin < xMin) {
                    xMin = tempXMin;
                }
                if (tempXMax > xMax) {
                    xMax = tempXMax;
                }
                if (tempYMin < yMin) {
                    yMin = tempYMin;
                }
                if (tempYMax > yMax) {
                    yMax = tempYMax;
                }
            });
            let minRange: number = Math.min(xMax - xMin, yMax - yMin);
            for (let edgePair of edgePairs) {
                let dis: number = TBaseRoomToolUtil.instance.calDistance(edgePair[0], edgePair[1]);
                // 这里排除范围不能单纯地直接使用index进行判断， 50是临时设置的,进行多边形减枝
                let tol: number = sameDirPolys.length > 1 ? 0.9 : 0.4;
                if (((!TBaseRoomToolUtil.instance.findNearLength(edgeLengths, dis) && isNearEdgeLengthMoreThanTol(edgePair, polygon.edges)) || dis / minRange > tol) && dis > 50) {
                    newEdgePair.push(edgePair);
                }
            }
            let minLengthInfo: any = calMinDistanByEdgePair(newEdgePair);
            if (allMinLen > minLengthInfo.minDis) {
                allMinLen = minLengthInfo.minDis;
                // 确定此边对共同部分
                commonEdgePairLen = calCommonLenByEdgePair(minLengthInfo.minEdgePair);
            }
        }
        // 过道最短距离+边对共同距离 共同决定得分
        return { minLen: allMinLen, commonMinLen: commonEdgePairLen };
    }

    // 计算图元的重叠度, 只考虑2d视角的方向
    public calOverlayRatioForFigures(figure1: TFigureElement, figure2: TFigureElement, isMatchRect: boolean = false): number {
        let rect1: ZRect = isMatchRect ? figure1.matched_rect || figure1.rect : figure1.rect;
        let rect2: ZRect = isMatchRect ? figure2.matched_rect || figure2.rect : figure2.rect;
        return this.calOverlayRatioForRects(rect1, rect2);
    }

    public calOverlayRatioForRects(rect1: ZRect, rect2: ZRect): number {
        // TODO 此接口还是可以继续进行完善的
        let box1 = this.getRange2dByPolygon(rect1);
        let box2 = this.getRange2dByPolygon(rect2);
        let overlayRatio: number = this.calOverlayRatioForBox2d(box1, box2);
        if (overlayRatio > 0) {
            // 再使用另外一种方式进行
            let intersectPolys: ZPolygon[] = rect1.intersect_polygons([rect2]);
            if (intersectPolys.length == 0) {
                return 0;
            }
        }
        return overlayRatio;
    }

    public isOnSideRangeContain(figure1: TFigureElement, figure2: TFigureElement, isMatchRect: boolean = false): boolean {
        let box1: any = getBoundingBox(figure1, isMatchRect);
        let box2: any = getBoundingBox(figure2, isMatchRect);

        return (box1.xMin <= box2.xMin && box1.xMax >= box2.xMax) || (box1.yMin <= box2.yMin && box1.yMax >= box2.yMax)
            || (box2.xMin <= box1.xMin && box2.xMax >= box1.xMax) || (box2.yMin <= box1.yMin && box2.yMax >= box1.yMax);
    }

    public calRatioForRangContain(figure1: TFigureElement, figure2: TFigureElement, isMatchRect: boolean = false): number {
        let rect1: ZRect = isMatchRect ? figure1.matched_rect || figure1.rect : figure1.rect;
        let rect2: ZRect = isMatchRect ? figure2.matched_rect || figure2.rect : figure2.rect;
        return this.calRatioForRangContainByRect(rect1, rect2);
    }

    public calRatioForRangContainByRect(rect1: ZRect, rect2: ZRect): number {
        let box1: any = this.getRange2dByPolygon(rect1);
        let box2: any = this.getRange2dByPolygon(rect2);
        if (box1.xMin <= box2.xMin && box1.xMax >= box2.xMax) {
            return Math.min(box2.xMin - box1.xMin, box1.xMax - box2.xMax) / (box1.xMax - box1.xMin);
        }
        if (box1.yMin <= box2.yMin && box1.yMax >= box2.yMax) {
            return Math.min(box2.yMin - box1.yMin, box1.yMax - box2.yMax) / (box1.yMax - box1.yMin);
        }
        if (box2.xMin <= box1.xMin && box2.xMax >= box1.xMax) {
            return Math.min(box1.xMin - box2.xMin, box2.xMax - box1.xMax) / (box2.xMax - box2.xMin);
        }
        if (box2.yMin <= box1.yMin && box2.yMax >= box1.yMax) {
            return Math.min(box1.yMin - box2.yMin, box2.yMax - box1.yMax) / (box2.yMax - box2.yMin);
        }
        return 0;
    }

    public calMinAreaForFigures(figure1: TFigureElement, figure2: TFigureElement): number {
        let box1: any = getBoundingBox(figure1);
        let box2: any = getBoundingBox(figure2);
        return Math.min(this.calAreaForBox2d(box1), this.calAreaForBox2d(box2))
    }

    // 计算区间的重合度
    public calOverlayRatioForBox2d(box2d1: any, box2d2: any) {
        if (!(box2d1.xMax > box2d2.xMin && box2d1.xMin < box2d2.xMax && box2d1.yMax > box2d2.yMin && box2d1.yMin < box2d2.yMax)) {
            return 0;
        }

        let intersectBox: any = this.calIntersectBox2d(box2d1, box2d2);
        if (!intersectBox) {
            return 0;
        }
        let intersectBoxArea: number = this.calAreaForBox2d(intersectBox);
        return intersectBoxArea / Math.min(this.calAreaForBox2d(box2d1), this.calAreaForBox2d(box2d2));
    }

    public calAreaForBox2d(box2d: any): number {
        let w: number = box2d.xMax - box2d.xMin;
        let h: number = box2d.yMax - box2d.yMin;
        return w * h;
    }

    public activeFunc(x: number): number {
        return x * (Math.max(0, -x) ? 1 : (1 + Math.max(0, -x)));
    }

    public isEdgeOverLayPolygon(edge: ZEdge, rect: ZRect, ratio: number = 0, isRoomEdge: boolean = false): boolean {
        let edgeDir: Vector3 = edge.dv.clone();
        let edgeStart: Vector3 = edge.v0.pos.clone().add(edgeDir.clone().multiplyScalar(edge.length * ratio));
        let edgeEnd: Vector3 = edge.v1.pos.clone().add(edgeDir.clone().multiplyScalar(-edge.length * ratio));
        let edgeXMin: number = Math.min(edgeStart.x, edgeEnd.x);
        let edgeXMax: number = Math.max(edgeStart.x, edgeEnd.x);
        let edgeYMin: number = Math.min(edgeStart.y, edgeEnd.y);
        let edgeYMax: number = Math.max(edgeStart.y, edgeEnd.y);

        let rectBox = this.getRange2dByPolygon(rect);
        if (edgeXMin < rectBox.xMax && edgeXMax > rectBox.xMin && edgeYMin < rectBox.yMax && edgeYMax > rectBox.yMin) {
            if (!isRoomEdge) {
                return true;
            }
            else {
                let xMinValue: number = Math.min(Math.abs(edgeXMin - rectBox.xMax), Math.abs(edgeXMax - rectBox.xMin));
                let yMinValue: number = Math.min(Math.abs(edgeYMin - rectBox.yMax), Math.abs(edgeYMax - rectBox.yMin));
                let minValue: number = Math.min(xMinValue, yMinValue);
                if (minValue < 5) {
                    return false;
                }
                return true;
            }
        }
        return false;
    }

    public getCenter3dByRange2d(range: any): Vector3 {
        let xCenter: number = (range.xMax + range.xMin) / 2;
        let yCenter: number = (range.yMax + range.yMin) / 2;
        return new Vector3(xCenter, yCenter, 0);
    }

    public getRectByRange2d(range: any, center: Vector3): ZRect
    {
        let len: number = range.xMax - range.xMin;
        let depth: number = range.yMax - range.yMin;
        let rect: ZRect = new ZRect(len, depth);
        rect.nor = new Vector3(0, 1, 0);
        rect.rect_center = center.clone();
        rect.updateRect();
        return rect;
    }

    public getLayonInfoRoomEdgesByFigure(room: TRoom, figure: TFigureElement): any {
        let layonRoomEdges: ZEdge[] = [];
        let rectEdgeTypes: RectEdgeType[] = [];
        for (let roomEdge of room.room_shape._poly.edges) {
            let edgeType: RectEdgeType = getTypeEdgeLayonRect(roomEdge, figure.rect);
            if (edgeType) {
                layonRoomEdges.push(roomEdge);
                rectEdgeTypes.push(edgeType);
            }
        }
        if (layonRoomEdges.length > 1) {
            let newLayonRoomEdges: ZEdge[] = [];
            let doors: I_Window[] = room.windows.filter(window => window.type == "Door");
            for (let layonRoomEdge of layonRoomEdges) {
                let hasDoor: boolean = false;
                for (let door of doors) {
                    let edgeType: RectEdgeType = getTypeEdgeLayonRect(layonRoomEdge, door.rect);
                    if (edgeType) {
                        hasDoor = true;
                    }
                }
                if (!hasDoor) {
                    newLayonRoomEdges.push(layonRoomEdge);
                }
            }
            layonRoomEdges = newLayonRoomEdges;
        }
        return { roomEdges: layonRoomEdges, figureEdgeTypes: rectEdgeTypes };
    }

    public getLayonInfoFigureEdgesByFigure(layonFigure: TFigureElement, figure: TFigureElement): any {
        let layonFigureEdges: ZEdge[] = [];
        let rectEdgeTypes: RectEdgeType[] = [];
        for (let figureEdge of layonFigure.rect.edges) {
            let edgeType: RectEdgeType = getTypeEdgeLayonRect(figureEdge, figure.rect);
            if (edgeType) {
                layonFigureEdges.push(figureEdge);
                rectEdgeTypes.push(edgeType);
            }
        }
        return { roomEdges: layonFigureEdges, figureEdgeTypes: rectEdgeTypes };
    }

    // 连续化， 需计算最小边对共同部分如果不会是全包含则计算为1+共同部分/最长边（暂时只处理图元与图元之间的距离）
    // TODO 还需要将悬空的图元加载进来一起进行计算，这个其实还算是比较重要的
    public calMinFlowDistance(room: TRoom, floorFigures: TFigureElement[], allFigures: TFigureElement[]): any {
        let maxRoomEdgeLen: number = Number.NEGATIVE_INFINITY;
        room.room_shape._poly.edges.forEach(edge => {
            if (edge.length > maxRoomEdgeLen) {
                maxRoomEdgeLen = edge.length;
            }
        });
        let doorDirVecs: Vector3[] = getRoomDoorDirs(room);
        let minFlowDistance: number = maxRoomEdgeLen;
        let extendRatio: number = 0;
        let doorDist: number = getWallNearDoorFigureDist(room, floorFigures);
        let minDistanceFigures: TFigureElement[] = null;
        if (minFlowDistance > doorDist) {
            minFlowDistance = doorDist;
        }

        for (let i = 0; i < floorFigures.length; ++i) {
            let layonInfo: any = this.getLayonInfoRoomEdgesByFigure(room, floorFigures[i]);
            let otherRoomEdges: ZEdge[] = getOtherRoomEdges(room, floorFigures[i], layonInfo);
            let otherFigures: TFigureElement[] = getOtherFiguresByRoomEdges(allFigures, otherRoomEdges, room);
            let figureRect: ZRect = floorFigures[i].rect;
            for (let otherFigure of otherFigures) {
                if ((!checkIsFloorFigure(otherFigure, floorFigures)) || isSameFigureGroup(floorFigures[i], otherFigure)) {
                    continue;
                }
                let isNeedTodCalPointMinDist: boolean = true;
                // TODO 这里额外计算ratio
                if (TBaseRoomToolUtil.instance.polygonIsInFrontArea(figureRect.leftEdge, figureRect.rightEdge, otherFigure.rect)) {
                    let dist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(figureRect.leftEdge, otherFigure.rect);
                    if (dist < minFlowDistance) {
                        // 确认共同部分占比
                        extendRatio = calRatioRangeByEdgeAndRect(figureRect.leftEdge, otherFigure.rect);
                        minDistanceFigures = [floorFigures[i], otherFigure];
                        if (isMainFlowForFigures(doorDirVecs, floorFigures[i], otherFigure, room, otherFigures)) {
                            minFlowDistance = dist;
                        }
                    }
                    isNeedTodCalPointMinDist = false;
                }
                if (TBaseRoomToolUtil.instance.polygonIsInFrontArea(figureRect.rightEdge, figureRect.leftEdge, otherFigure.rect)) {
                    let dist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(figureRect.rightEdge, otherFigure.rect);
                    if (dist < minFlowDistance) {
                        extendRatio = calRatioRangeByEdgeAndRect(figureRect.rightEdge, otherFigure.rect);
                        minDistanceFigures = [floorFigures[i], otherFigure];
                        if (isMainFlowForFigures(doorDirVecs, floorFigures[i], otherFigure, room, otherFigures)) {
                            minFlowDistance = dist;
                        }
                    }
                    isNeedTodCalPointMinDist = false;
                }
                if (TBaseRoomToolUtil.instance.polygonIsInFrontArea(figureRect.frontEdge, figureRect.backEdge, otherFigure.rect)) {
                    let dist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(figureRect.frontEdge, otherFigure.rect);
                    if (dist < minFlowDistance) {
                        extendRatio = calRatioRangeByEdgeAndRect(figureRect.frontEdge, otherFigure.rect);
                        minDistanceFigures = [floorFigures[i], otherFigure];
                        if (isMainFlowForFigures(doorDirVecs, floorFigures[i], otherFigure, room, otherFigures)) {
                            minFlowDistance = dist;
                        }
                    }
                    isNeedTodCalPointMinDist = false;
                }
                if (TBaseRoomToolUtil.instance.polygonIsInFrontArea(figureRect.backEdge, figureRect.frontEdge, otherFigure.rect)) {
                    let dist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(figureRect.backEdge, otherFigure.rect);
                    if (dist < minFlowDistance) {

                        extendRatio = calRatioRangeByEdgeAndRect(figureRect.backEdge, otherFigure.rect);
                        minDistanceFigures = [floorFigures[i], otherFigure];
                        if (isMainFlowForFigures(doorDirVecs, floorFigures[i], otherFigure, room, otherFigures)) {
                            minFlowDistance = dist;
                        }
                    }
                    isNeedTodCalPointMinDist = false;
                }
                if (isNeedTodCalPointMinDist && isMainFlowForFigures(doorDirVecs, floorFigures[i], otherFigure, room, otherFigures)) {
                    let pointDist: number = calDistanceByFigurePoint(floorFigures[i], otherFigure);
                    if (pointDist < minFlowDistance) {
                        minFlowDistance = pointDist;
                    }
                }
            }
            for (let otherRoomEdge of otherRoomEdges) {
                // if(TBaseRoomToolUtil.instance.edgeIsInFrontArea(figureRect.leftEdge, figureRect.rightEdge, otherRoomEdge))
                // {
                //     let dist: number = calDistanceByParallelEdges(figureRect.leftEdge, otherRoomEdge);
                //     if(dist && dist < minFlowDistance)
                //     {
                //         minFlowDistance = dist;
                //     }
                // }
                // if(TBaseRoomToolUtil.instance.edgeIsInFrontArea(figureRect.rightEdge, figureRect.leftEdge, otherRoomEdge))
                // {
                //     let dist: number = calDistanceByParallelEdges(figureRect.rightEdge, otherRoomEdge);
                //     if(dist && dist < minFlowDistance)
                //     {
                //         minFlowDistance = dist;
                //     }
                // }
                if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(figureRect.frontEdge, figureRect.backEdge, otherRoomEdge)) {
                    let dist: number = calDistanceByParallelEdges(figureRect.frontEdge, otherRoomEdge);
                    if (dist && dist < minFlowDistance) {
                        minFlowDistance = dist;
                    }
                }
                // if(TBaseRoomToolUtil.instance.edgeIsInFrontArea(figureRect.backEdge, figureRect.frontEdge, otherRoomEdge))
                // {
                //     let dist: number = calDistanceByParallelEdges(figureRect.backEdge, otherRoomEdge);
                //     if(dist && dist < minFlowDistance)
                //     {
                //         minFlowDistance = dist;
                //     }
                // }
            }
        }
        return { minLen: minFlowDistance, extendRatio: extendRatio, fineTuningFigures: minDistanceFigures };
    }

    public getOtherFigures(currentFigure: TFigureElement, figures: TFigureElement[]): TFigureElement[] {
        let otherFigures: TFigureElement[] = [];
        figures.forEach(figure => {
            if (figure != currentFigure) {
                otherFigures.push(figure);
            }
        });
        return otherFigures;
    }


    public calOcclusionWindonLen(window: I_Window, figure: TFigureElement, isMatchedRect: boolean = false): any {
        let occlusionWindowLen: number = 0;
        let isLayOn: boolean = isUseLayonWindow(window, figure, isMatchedRect);
        let figureRect: ZRect = isMatchedRect ? figure.matched_rect || figure.rect : figure.rect;
        if (isLayOn) {
            let lResult: { layon_len?: number, ll?: number, rr?: number } = {};
            let distTol: number = 5;
            if(window.realType == "SlidingDoor")
            {
                distTol = 200;
            }
            let layOnEdgeInfo: any = TBaseRoomToolUtil.instance.isLayOnPolygons(figureRect, window.rect, lResult, TBaseRoomToolUtil.instance.tol, distTol);
            if (layOnEdgeInfo) {
                occlusionWindowLen = lResult.layon_len;
            }
        }
        else {
            let doorDir: Vector3 = window.rect.backEdge.dv;
            let doorNor: Vector3 = window.rect.backEdge.nor;
            let doorStartPoint: Vector3 = window.rect.backEdge.v0.pos.clone().add(window.rect.frontEdge.v1.pos).multiplyScalar(0.5);
            let doorLen: number = window.length;
            // 检查是否同侧,占门的长度
            let isOverlayDoor: boolean = isOverlayWithDoor(doorStartPoint, doorDir, doorNor, doorLen, figure, isMatchedRect);
            if (isOverlayDoor) {
                let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(figureRect);
                let doorRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(window.rect);
                if (doorRange.xMin < figureRange.xMax && figureRange.xMin < doorRange.xMax) {
                    let xMin: number = Math.max(figureRange.xMin, doorRange.xMin);
                    let xMax: number = Math.min(figureRange.xMax, doorRange.xMax);
                    let tempLen: number = xMax - xMin;
                    if (occlusionWindowLen < tempLen) {
                        occlusionWindowLen = tempLen;
                    }
                }
                if (doorRange.yMin < figureRange.yMax && figureRange.yMin < doorRange.yMax) {
                    let yMin: number = Math.max(figureRange.yMin, doorRange.yMin);
                    let yMax: number = Math.min(figureRange.yMax, doorRange.yMax);
                    let tempLen: number = yMax - yMin;
                    if (occlusionWindowLen < tempLen) {
                        occlusionWindowLen = yMax - yMin;
                    }
                }
            }
        }

        return {
            occlusionLen: occlusionWindowLen, isLayonBack: (Math.abs(window.rect.backEdge.dv.clone().dot(figureRect.backEdge.dv)) > 0.9) && (
                (TBaseRoomToolUtil.instance.calDistance(window.rect.backEdge, figureRect.backEdge) < 1))
        };
    }

    public edgeIsInEdgesArea(edge1: ZEdge, edge2: ZEdge, otherEdge: ZEdge): boolean {
        let edgeRange1: any = getEdgeRange(edge1);
        let edgeRange2: any = getEdgeRange(edge2);
        let edgeAreaRange: any = {
            xMin: Math.min(edgeRange1.xMin, edgeRange2.xMin), xMax: Math.max(edgeRange1.xMax, edgeRange2.xMax),
            yMin: Math.min(edgeRange1.yMin, edgeRange2.yMin), yMax: Math.max(edgeRange1.yMax, edgeRange2.yMax)
        };
        let otherEdgeRange: any = getEdgeRange(otherEdge);
        return otherEdgeRange.xMin < edgeAreaRange.xMax && otherEdgeRange.xMax > edgeAreaRange.xMin && otherEdgeRange.yMin < edgeAreaRange.yMax && otherEdgeRange.yMax > edgeAreaRange.yMin;
    }

    public calOverRoomRation(room: TRoom, figures: TFigureElement[], overLayWallTol: number, overlayRatio: number, overlayFigures: TFigureElement[], isMatchRect: boolean = false): number {
        let allOverRatio: number = overlayRatio;
        for (let figure of figures) {
            let tempOverRation = ratioOfFigureOverRoom(room, figure, overLayWallTol, isMatchRect);
            if (tempOverRation > 0) {
                overlayFigures.push(figure);
                allOverRatio += (tempOverRation * 10);
            }
        }
        return allOverRatio;
    }

    public checkAbnormalFigures(room: TRoom, layoutScores: I_LayoutScore[], layoutFigures: TFigureElement[], abnormalRuleNames: string[]) {
        if (!LayoutAI_App.IsDebug) {
            return;
        }
        let abnormalFigures: TFigureElement[] = getAbnormalFigures(layoutScores, abnormalRuleNames);
        let sourceFigures: TFigureElement[] = this.getAllSingleFigureFromGroup(room._furniture_list);
        if (layoutFigures) {
            for (let sourceFigure of sourceFigures) {
                sourceFigure.isAbnormalFigure = false;
                if (isSameFigure(sourceFigure, abnormalFigures)) {
                    sourceFigure.isAbnormalFigure = true;
                }
            }
        }
    }

    public getAllSingleFigureFromGroup(figures: TFigureElement[]): TFigureElement[] {
        let allSingleFigures: TFigureElement[] = [];
        if (!figures) {
            return allSingleFigures;
        }
        for (let figure of figures) {
            if (!figure.category.includes("组合") || !(figure.furnitureEntity instanceof TBaseGroupEntity)) {
                allSingleFigures.push(figure);
            }
            else {
                let groupEntity: TBaseGroupEntity = figure.furnitureEntity as TBaseGroupEntity;
                if (groupEntity) {
                    for (let furnitureEntity of groupEntity.combination_entitys) {
                        allSingleFigures.push(furnitureEntity.figure_element);
                    }
                }
            }
        }
        allSingleFigures = Array.from(new Set(allSingleFigures));
        return allSingleFigures;
    }

    // TODO 这个实际上还是继续进行完善，因为使用图元包围盒判断图元是并不是最佳的，如果图元发生旋转则不适用
    public calOverlayRatioByFigures(figure1: TFigureElement, figure2: TFigureElement, overlayRatio: number, isOverlayOneSide: boolean = true, isMatchRect: boolean = false): number {
        let tempOverlayRatio: number = TBaseRoomToolUtil.instance.calOverlayRatioForFigures(figure1, figure2, isMatchRect);
        if (tempOverlayRatio == 1 || (isOverlayOneSide && TBaseRoomToolUtil.instance.isOnSideRangeContain(figure1, figure2, isMatchRect))) {
            let rect1: ZRect = isMatchRect ? figure1.matched_rect || figure1.rect : figure1.rect;
            let rect2: ZRect = isMatchRect ? figure2.matched_rect || figure2.rect : figure2.rect;
            let leftLen: number = TBaseRoomToolUtil.instance.calDistance(rect1.leftEdge, rect2.leftEdge);
            let rightLen: number = TBaseRoomToolUtil.instance.calDistance(rect1.rightEdge, rect2.rightEdge);
            if (tempOverlayRatio == 1) {
                let minLen: number = Math.min(leftLen, rightLen);
                let extendRatio: number = minLen / Math.max(rect1.backEdge.length, rect2.backEdge.length);
                overlayRatio += (extendRatio * 10);
            }
            else {
                let extendRatio: number = TBaseRoomToolUtil.instance.calRatioForRangContain(figure1, figure2, isMatchRect);
                extendRatio = extendRatio ? extendRatio : 0.5;
                overlayRatio += (extendRatio * 10);
            }
        }
        overlayRatio += tempOverlayRatio;
        return overlayRatio;
    }

    public calOverlayRatioByRects(rect1: ZRect, rect2: ZRect): number {
        let overlayRatio: number = 0;
        let tempOverlayRatio: number = TBaseRoomToolUtil.instance.calOverlayRatioForRects(rect1, rect2);
        if (tempOverlayRatio == 1) {
            let leftLen: number = TBaseRoomToolUtil.instance.calDistance(rect1.leftEdge, rect2.leftEdge);
            let rightLen: number = TBaseRoomToolUtil.instance.calDistance(rect1.rightEdge, rect2.rightEdge);
            if (tempOverlayRatio == 1) {
                let minLen: number = Math.min(leftLen, rightLen);
                let extendRatio: number = minLen / Math.max(rect1.backEdge.length, rect2.backEdge.length);
                overlayRatio += (extendRatio * 10);
            }
            else {
                let extendRatio: number = TBaseRoomToolUtil.instance.calRatioForRangContainByRect(rect1, rect2);
                extendRatio = extendRatio ? extendRatio : 0.5;
                overlayRatio += (extendRatio * 10);
            }
        }
        overlayRatio += tempOverlayRatio;
        return overlayRatio;
    }

    public calAverageCenterByFigures(figures: TFigureElement[]): Vector3 {
        let figureRects: ZRect[] = this.getRectsByFigures(figures);
        return this.calAverageCenterByRects(figureRects);
    }

    public calAverageCenterByRects(rects: ZRect[]): Vector3 {
        let targetCenter: Vector3 = new Vector3(0, 0, 0);
        for (let rect of rects) {
            targetCenter.add(rect.rect_center.clone());
        }
        targetCenter.multiplyScalar(1 / rects.length);
        return targetCenter;
    }

    public getEdgeTypeFromRect(rect: ZRect, edge: ZEdge): RectEdgeType {
        let edgeType: RectEdgeType = null;
        switch (edge) {
            case rect.frontEdge:
                {
                    edgeType = RectEdgeType.k_front;
                    break;
                }
            case rect.backEdge:
                {
                    edgeType = RectEdgeType.k_back;
                    break;
                }
            case rect.leftEdge:
                {
                    edgeType = RectEdgeType.k_left;
                    break;
                }
            case rect.rightEdge:
                {
                    edgeType = RectEdgeType.k_right;
                    break;
                }
            default:
                break;
        }
        return edgeType;
    }

    public isViewInDoor(figure: TFigureElement, otherFigures: TFigureElement[], door: I_Window, faceDoorRatio: number): boolean {
        if (!TBaseRoomToolUtil.instance.isInRangeByEdgeForPolygon(door.rect.frontEdge, figure.rect, faceDoorRatio)) {
            return false;
        }
        let toiletBowlWithDoorDis: number = calDistanceFromFigureToDoor(door.rect.frontEdge, figure);
        let toiletBowlProjectRange: any = calFigureProjectDoor(door.rect.frontEdge, figure);
        if (!toiletBowlProjectRange) {
            return false;
        }
        let otherProjectRangs: any = [];
        for (let otherFigure of otherFigures) {
            let otherFigureWithDoorDis: number = calDistanceFromFigureToDoor(door.rect.frontEdge, otherFigure);
            let otherFigureProjectRange: any = calFigureProjectDoor(door.rect.frontEdge, otherFigure);
            if (otherFigureWithDoorDis > toiletBowlWithDoorDis || !otherFigureProjectRange) {
                continue;
            }
            if (!otherProjectRangs.length) {
                otherProjectRangs.push(otherFigureProjectRange);
            }
            else {
                otherProjectRangs = updateOtherProjectRanges(otherProjectRangs, otherFigureProjectRange);
            }
        }
        for (let otherProjectRang of otherProjectRangs) {
            if (otherProjectRang.xMin <= toiletBowlProjectRange.xMin && otherProjectRang.xMax >= toiletBowlProjectRange.xMax) {
                return false;
            }
        }

        return true;
    }

    public getBoxRangByFigurs(figures: TFigureElement[], isMatchedRect: boolean = false): any {
        if (!figures.length) {
            return null;
        }
        let figureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(isMatchedRect ? figures[0].matched_rect || figures[0].rect : figures[0].rect);
        for (let figure of figures) {
            let tempFigureRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(isMatchedRect ? figure.matched_rect || figure.rect : figure.rect);
            if (tempFigureRange.xMax > figureRange.xMax) {
                figureRange.xMax = tempFigureRange.xMax;
            }
            if (tempFigureRange.xMin < figureRange.xMin) {
                figureRange.xMin = tempFigureRange.xMin;
            }
            if (tempFigureRange.yMax > figureRange.yMax) {
                figureRange.yMax = tempFigureRange.yMax;
            }
            if (tempFigureRange.yMin < figureRange.yMin) {
                figureRange.yMin = tempFigureRange.yMin;
            }
        }
        return figureRange;
    }

    public calCenterByRange(range: any): Vector3 {
        let center: Vector3 = new Vector3((range.xMin + range.xMax) / 2, (range.yMin + range.yMax) / 2, 0);
        return center;
    }

    public isOverlayRange2ds(range1: any, range2: any, isEqual: boolean = true): boolean
    {
        if(isEqual)
        {
            if(range1.xMin <= range2.xMax && range1.xMax >= range2.xMin && range1.yMin <= range2.yMax && range1.yMax >= range2.yMin)
            {
                return true;
            }
        }
        else
        {
            // TODO 过道这里加个小小的限制
            let distTol: number = 5;
            if((range1.xMin < range2.xMax && range1.xMax > range2.xMin && range1.yMin < range2.yMax && range1.yMax > range2.yMin) &&
                (Math.abs(range1.xMin - range2.xMax) > distTol && Math.abs(range1.xMax - range2.xMin) > distTol && Math.abs(range1.yMin - range2.yMax) > distTol &&
                 Math.abs(range1.yMax - range2.yMin) > distTol))
            {

                return true;
            }
        }
        return false;
    }

    public getDoorInfos(room: TRoom): Map<DoorType, I_Window[]>
    {
        let doorInfos: Map<DoorType, I_Window[]> = new Map<DoorType, I_Window[]>();
        let doors: I_Window[] = room.windows.filter(window => window.type == "Door");
        for(let door of doors)
        {
            if(door.room_names?.length == 1)
            {
                if(!doorInfos.has(DoorType.k_none))
                {
                    doorInfos.set(DoorType.k_none, [door]);
                }
                else{
                    doorInfos.get(DoorType.k_none).push(door);
                }
            }
            else
                {
                if(door.room_names?.includes("阳台"))
                {
                    if(!doorInfos.has(DoorType.k_balcony))
                    {
                        doorInfos.set(DoorType.k_balcony, [door]);
                    }
                    else{
                        doorInfos.get(DoorType.k_balcony).push(door);
                    }
                }
                else if(door.room_names?.includes("厨房"))
                {
                    if(!doorInfos.has(DoorType.k_kitchen))
                    {
                        doorInfos.set(DoorType.k_kitchen, [door]);
                    }
                    else{
                        doorInfos.get(DoorType.k_kitchen).push(door);
                    }
                }
                else if(door.room_names?.includes("卧室"))
                {
                    if(!doorInfos.has(DoorType.k_bedRoom))
                    {
                        doorInfos.set(DoorType.k_bedRoom, [door]);
                    }
                    else{
                        doorInfos.get(DoorType.k_bedRoom).push(door);
                    }
                }
                else if(door.room_names?.includes("卫生间"))
                {
                    if(!doorInfos.has(DoorType.k_bathRoom))
                    {
                        doorInfos.set(DoorType.k_bathRoom, [door]);
                    }
                    else{
                        doorInfos.get(DoorType.k_bathRoom).push(door);
                    }
                }
            }
        }
        return doorInfos;
    }

    public getRoomEdgeWithDoorInfos(roomExpandPolygon: ZPolygon, doorInfo: Map<DoorType, I_Window[]>): Map<DoorType, ZEdge[]>
    {
        let roomEdgeWithDoorInfos: Map<DoorType, ZEdge[]> = new Map<DoorType, ZEdge[]>();
        for(let entry of doorInfo) 
        {
            let roomEdgeWithDoors: ZEdge[] = [];
            for(let door of entry[1])
            {
                let minDistance: number = Number.POSITIVE_INFINITY;
                let nearRoomEdge: ZEdge = null;
                for(let roomEdge of roomExpandPolygon.edges)
                {
                    let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
                    let doorFrontEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(door.rect.frontEdge);
                    let isContain: boolean = (
                        roomEdgeRange.xMin <= doorFrontEdgeRange.xMax && roomEdgeRange.xMax >= doorFrontEdgeRange.xMin) 
                        || (roomEdgeRange.yMin <= doorFrontEdgeRange.yMax && roomEdgeRange.yMax >= doorFrontEdgeRange.yMin);
                    if(Math.abs(roomEdge.dv.clone().dot(door.rect.frontEdge.dv)) < 0.9 || !isContain)
                    {
                        continue;
                    }
                    let dist: number = TBaseRoomToolUtil.instance.calDistance(roomEdge, door.rect.frontEdge);
                    if(dist < minDistance)
                    {
                        minDistance = dist;
                        nearRoomEdge = roomEdge;
                    }
                }
                if(nearRoomEdge)
                {
                    roomEdgeWithDoors.push(nearRoomEdge);
                }
            }
            roomEdgeWithDoorInfos.set(entry[0], roomEdgeWithDoors);
        }
        return roomEdgeWithDoorInfos;
    }

    public calMatchFigureOverlayDoorRation(room: TRoom, figures: TFigureElement[]): number
    {
        let windows: I_Window[] = room.windows.filter(window => window.type == "Door");
        if(windows.length == 0)
        {
            return 0;
        }
        let extendRatio: number = 0;
        let figureLlen: number = 0;
        for(let window of windows)
        {
            for(let figure of figures)
            {
                let occlusionWindowInfo: any = this.calOcclusionWindonLen(window, figure, true);
                if(!occlusionWindowInfo.occlusionLen)
                {
                    continue;
                }
                if(occlusionWindowInfo.isLayonBack)
                {
                    if(occlusionWindowInfo.occlusionLen / Math.min(figure.rect.length, window.rect.length) == 1)
                    {
                        let windowLeft: number = Math.min(TBaseRoomToolUtil.instance.calDistance(figure.rect.leftEdge, window.rect.leftEdge),
                            TBaseRoomToolUtil.instance.calDistance(figure.rect.rightEdge, window.rect.leftEdge));
                        let windowRight: number = Math.min(TBaseRoomToolUtil.instance.calDistance(figure.rect.leftEdge, window.rect.rightEdge),
                            TBaseRoomToolUtil.instance.calDistance(figure.rect.rightEdge, window.rect.rightEdge));
                        extendRatio += (1 + Math.min(windowLeft, windowRight) / figure.rect.backEdge.length);
                    }
                }
                else
                {
                    let norDis: number = Number.POSITIVE_INFINITY;
                    for(let vertex of figure.rect.vertices)
                    {
                        let dis: number = Math.abs(vertex.pos.clone().sub(window.rect.backEdge.v0.pos).dot(window.rect.backEdge.nor));
                        if(dis < norDis)
                        {
                            norDis = dis;
                        }
                    }
                    extendRatio += (1 + (window.length - norDis)/window.length);
                }
                figureLlen += occlusionWindowInfo.occlusionLen;
            }
        }
        let windowLength: number = 0;
        windows.forEach(window => windowLength += window.length);
        let ratio: number = figureLlen / windowLength + extendRatio;
        return ratio;
    }

    public isParallelTwoEdges(edge1: ZEdge, edge2: ZEdge, tol: number = 0.9): boolean
    {
        if(Math.abs(edge1.dv.clone().dot(edge2.dv)) > tol)
        {
            return true;
        }
        return false;
    }

    public getAllFiguresFromGroupTemplates(groupTemplates: TGroupTemplate[]): TFigureElement[]
    {
        let allFigures: TFigureElement[] = [];
        for(let groupTemplate of groupTemplates)
        {
            let seedFigureGroup = groupTemplate.current_s_group;
            if(!seedFigureGroup)
            {
                continue;
            }
            allFigures.push(seedFigureGroup.main_figure);
            for(let subFigure of seedFigureGroup.sub_figures)
            {
                allFigures.push(subFigure.figure);
            }
        }
        return allFigures;
    }

    public calIntersectBox2d(box2d1: any, box2d2: any): any {
        if (!(box2d1.xMax > box2d2.xMin && box2d1.xMin < box2d2.xMax && box2d1.yMax > box2d2.yMin && box2d1.yMin < box2d2.yMax)) {
            return null;
        }
        let maxBoxMinX: number = Math.max(box2d1.xMin, box2d2.xMin);
        let minBoxMaxX: number = Math.min(box2d1.xMax, box2d2.xMax);
        let maxBoxMinY: number = Math.max(box2d1.yMin, box2d2.yMin);
        let minBoxMaxY: number = Math.min(box2d1.yMax, box2d2.yMax);
    
        return { xMin: maxBoxMinX, xMax: minBoxMaxX, yMin: maxBoxMinY, yMax: minBoxMaxY };
    }

    public isNearEdges(edge1: ZEdge, edge2: ZEdge, tol: number = 1): boolean
    {
        return edge1.v0.pos.distanceTo(edge2.v0.pos) < tol || edge1.v0.pos.distanceTo(edge2.v1.pos) < tol || edge1.v1.pos.distanceTo(edge2.v0.pos) < tol || edge1.v1.pos.distanceTo(edge2.v1.pos) < tol;
    }
        
    public expandeRangeToWall(sourceRanges: Map<Vector3, I_Range2D>, room: TRoom, otherRanges: Map<Vector3, I_Range2D>, isCut: boolean = true): Map<Vector3, I_Range2D>
    {
        let expandRanges: Map<Vector3, I_Range2D> = new Map<Vector3, I_Range2D>();
        let otherRects: ZRect[] = [];
        if(otherRanges)
        {
            Array.from(otherRanges?.values()).forEach((otherRange: any) => {
                let otherRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(otherRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(otherRange));
                otherRects.push(otherRect);
            });
        }
        for(let entry of sourceRanges.entries())
        {
            let sourceRange: I_Range2D = entry[1];
            let copySourceRange: any = this.cloneRange(sourceRange);
            baseExpandRangeToWall(copySourceRange, room, otherRects);
            // cut
            if(isCut)
            {
                let sourceCenter: Vector3 = entry[0].clone();
                postCutRangeByRoom(room, copySourceRange, sourceRange, sourceCenter);
            }
            expandRanges.set(entry[0], copySourceRange);
        }
        return expandRanges;
    }

    public isVaildRange(sourceRange: any): boolean
    {
        if(sourceRange.xMin < sourceRange.xMax && sourceRange.yMin < sourceRange.yMax)
        {
            return true;
        }
        return false;
    }

    public cloneRange(range: any): any
    {
        let cloneRange: any = {};
        cloneRange.xMin = range.xMin;
        cloneRange.xMax = range.xMax;
        cloneRange.yMin = range.yMin;
        cloneRange.yMax = range.yMax;
        return cloneRange;
    }

    public isSameRect(rect1: ZRect, rect2: ZRect, tol: number = 5): boolean
    {
        if(!rect1 && !rect2)
        {
            return true;
        }
        let isSame: boolean = false;
        if(rect1 && rect2)
        {
            let rectRange1: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect1);
            let rectRange2: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect2);
            if(Math.abs(rectRange1.xMin - rectRange2.xMin) < tol && Math.abs(rectRange1.xMax - rectRange2.xMax) < tol 
                && Math.abs(rectRange1.yMin - rectRange2.yMin) < tol && Math.abs(rectRange1.yMax - rectRange2.yMax) < tol)
            {
                isSame = true;
            }
        }
        return isSame;
    }

    public getMaxInnerRectByPolygon(subPoly: ZPolygon, tol: number = null,strategy:"MaxArea"|"MaxMinHH"="MaxMinHH"): ZRect
    {
        let updateValues: any = (values: number[], value: number) => {
            for(let valueItem of values)
            {
                if(Math.abs(valueItem  - value) < 0.01)
                {
                    return;
                }
            }
            values.push(value);
        }
        // 这种带记录的最难进行维护
        let maxArea: number = null;
        let maxMinHH : number = null;
        let maxAreaSplitEdge: ZEdge = null;
        let maxAreaDist: number = null;
        let targetBaseEdgeNor: Vector3 = null;
        for(let subEdge of subPoly.edges)
        {
            let projectXs: number[] = [0, subEdge.length];
            for(let otherSubEdge of subPoly.edges)
            {
                let dot: number = subEdge.nor.clone().dot(otherSubEdge.nor);
                if(dot > -0.9)
                {
                    continue;
                }
                let projectV0: any = subEdge.projectEdge2d(otherSubEdge.v0.pos);
                let projectV1: any = subEdge.projectEdge2d(otherSubEdge.v1.pos);
                if(projectV0.x < 0)
                {
                    projectV0.x = 0;
                }
                else if(projectV0.x > subEdge.length)
                {
                    projectV0.x = subEdge.length;
                }
                if(projectV1.x < 0)
                {
                    projectV1.x = 0;
                }
                else if(projectV1.x > subEdge.length)
                {
                    projectV1.x = subEdge.length;
                }
                updateValues(projectXs, projectV0.x);
                updateValues(projectXs, projectV1.x);
            }
            projectXs.sort((a, b) => { return a - b; });
            // 对数据分段之后再寻找此分割正对着的边最小距离，并组成矩形区域
            for(let i = 0; i < projectXs.length; i++)
            {
                for(let j = i + 1; j < projectXs.length; j++)
                {
                    let start: Vector3 = subEdge.unprojectEdge2d({x: projectXs[i], y: 0});
                    let end: Vector3 = subEdge.unprojectEdge2d({x: projectXs[j], y: 0});
                    let tempEdge: ZEdge = new ZEdge({pos: start}, {pos: end});
                    tempEdge.computeNormal();
                    let tempEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(tempEdge);
                    let minDist: number = null;
                    for(let faceEdge of subPoly.edges)
                    {
                        if(faceEdge.nor.clone().dot(subEdge.nor) > -0.9)
                        {
                            continue;
                        }
                        let dist: number = TBaseRoomToolUtil.instance.calDistance(tempEdge, faceEdge);
                        let faceEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(faceEdge);
                        if(((faceEdgeRange.xMin < tempEdgeRange.xMax && faceEdgeRange.xMax > tempEdgeRange.xMin) || (faceEdgeRange.yMin < tempEdgeRange.yMax && faceEdgeRange.yMax > tempEdgeRange.yMin)) 
                            && (minDist == null || dist < minDist))
                        {
                            minDist = dist;                            
                        }
                    }
                    if(tol && Math.min(tempEdge.length, minDist) / Math.max(tempEdge.length, minDist) < tol)
                    {
                        continue;
                    }
                    let area: number = minDist * tempEdge.length;
                    let minHH : number = Math.min(minDist, tempEdge.length);
                    let maxHH: number = Math.max(minDist, tempEdge.length);
                    if(strategy === "MaxArea")
                    {
                        if(maxArea == null || area > maxArea)
                        {
                            maxArea = area;
                            maxAreaSplitEdge = tempEdge;
                            maxAreaDist = minDist;
                            targetBaseEdgeNor = subEdge.nor.clone().negate();
                        }
                    }
                    else if(strategy === "MaxMinHH")
                    {
                        if(maxMinHH == null || minHH - maxMinHH > -10)
                        {
                            maxMinHH = minHH;
                            maxAreaSplitEdge = tempEdge;
                            maxAreaDist = minDist;
                            targetBaseEdgeNor = subEdge.nor.clone().negate();
                        }
                    }

                }
            }
        }
        if(maxAreaDist == null || maxAreaSplitEdge == null)
        {
            return null;
        }
        let maxAreaRect: ZRect = new ZRect(maxAreaSplitEdge.length, maxAreaDist + 0.01);
        maxAreaRect.nor = targetBaseEdgeNor.clone();
        maxAreaRect.back_center = maxAreaSplitEdge.center.clone();
        maxAreaRect.updateRect();
        return maxAreaRect;
    }

    public getAllInnerRectByPolygon(subPoly: ZPolygon): ZRect[]
    {
        let updateValues: any = (values: number[], value: number) => {
            for(let valueItem of values)
            {
                if(Math.abs(valueItem  - value) < 0.01)
                {
                    return;
                }
            }
            values.push(value);
        }

        let allRects: ZRect[] = [];
        for(let subEdge of subPoly.edges)
        {
            let projectXs: number[] = [0, subEdge.length];
            for(let otherSubEdge of subPoly.edges)
            {
                let dot: number = subEdge.nor.clone().dot(otherSubEdge.nor);
                if(dot > -0.9)
                {
                    continue;
                }
                let projectV0: any = subEdge.projectEdge2d(otherSubEdge.v0.pos);
                let projectV1: any = subEdge.projectEdge2d(otherSubEdge.v1.pos);
                if(projectV0.x < 0)
                {
                    projectV0.x = 0;
                }
                else if(projectV0.x > subEdge.length)
                {
                    projectV0.x = subEdge.length;
                }
                if(projectV1.x < 0)
                {
                    projectV1.x = 0;
                }
                else if(projectV1.x > subEdge.length)
                {
                    projectV1.x = subEdge.length;
                }
                updateValues(projectXs, projectV0.x);
                updateValues(projectXs, projectV1.x);
            }
            projectXs.sort((a, b) => { return a - b; });
            // 对数据分段之后再寻找此分割正对着的边最小距离，并组成矩形区域
            for(let i = 0; i < projectXs.length; i++)
            {
                for(let j = i + 1; j < projectXs.length; j++)
                {
                    let start: Vector3 = subEdge.unprojectEdge2d({x: projectXs[i], y: 0});
                    let end: Vector3 = subEdge.unprojectEdge2d({x: projectXs[j], y: 0});
                    let tempEdge: ZEdge = new ZEdge({pos: start}, {pos: end});
                    tempEdge.computeNormal();
                    let tempEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(tempEdge);
                    let minDist: number = null;
                    for(let faceEdge of subPoly.edges)
                    {
                        if(faceEdge.nor.clone().dot(subEdge.nor) > -0.9)
                        {
                            continue;
                        }
                        let dist: number = TBaseRoomToolUtil.instance.calDistance(tempEdge, faceEdge);
                        let faceEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(faceEdge);
                        if(((faceEdgeRange.xMin < tempEdgeRange.xMax && faceEdgeRange.xMax > tempEdgeRange.xMin) || (faceEdgeRange.yMin < tempEdgeRange.yMax && faceEdgeRange.yMax > tempEdgeRange.yMin)) 
                            && (minDist == null || dist < minDist))
                        {
                            minDist = dist;                            
                        }
                    }
                    let ratio: number  = Math.min(tempEdge.length, minDist) / Math.max(tempEdge.length, minDist);
                    if(ratio > 0.2)
                    {
                        let rect: ZRect = new ZRect(tempEdge.length, minDist);
                        rect.nor = tempEdge.nor.clone().negate();
                        rect.back_center = tempEdge.center.clone();
                        rect.updateRect();
                        if(!isInAllRects(allRects, tempEdge.length, minDist, rect.rect_center))
                        {
                            allRects.push(rect);
                        }
                        else
                        {
                            rect = null;
                        }
                    }
                }
            }
        }
        
        return allRects;
    }

    public calPolygonArea(polygon: ZPolygon): number
    {
        let vertices: Vector3[] = polygon.vertices.map(vertex => vertex.pos);
        const n = vertices.length;
        let area = 0;
        for (let i = 0; i < n; i++) {
            const j = (i + 1) % n;
            area += vertices[i].x * vertices[j].y;
            area -= vertices[j].x * vertices[i].y;
        }
        return Math.abs(area) / 2;
    }

    public calRangeArea(range: I_Range2D): number
    {
        let len: number = range.xMax - range.xMin;
        let depth: number = range.yMax - range.yMin;
        return len * depth;
    }

    public logRoomAndPolys(roomPoly: ZPolygon, polys: ZPolygon[], name: string) {
        console.log("---------------------" + name + "---------------------");
        let roomXs: number[] = [];
        let roomYs: number[] = [];
        for (let edge of roomPoly.edges) {
            roomXs.push(edge.v0.pos.x);
            roomXs.push(edge.v1.pos.x);
            roomYs.push(edge.v0.pos.y);
            roomYs.push(edge.v1.pos.y);
        }
        console.log("roomXs:  " + roomXs);
        console.log("roomYs:  " + roomYs);
    
        for(let i = 0; i < polys.length; ++i)
        {
            console.log("---------------------" + i + "---------------------");
            let polyXs: number[] = [];
            let polyYs: number[] = [];
            for (let edge of polys[i].edges) {
                polyXs.push(edge.v0.pos.x);
                polyXs.push(edge.v1.pos.x);
                polyYs.push(edge.v0.pos.y);
                polyYs.push(edge.v1.pos.y);
            }
            console.log("polyXs:  " + polyXs);
            console.log("polyYs:  " + polyYs);
        }
    }
}

function getEdgeRange(edge: ZEdge): any {
    let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
    let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
    let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
    let yMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);
    return { xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax };
}

function checkIsFloorFigure(targetFigure: TFigureElement, floorFigures: TFigureElement[]): boolean {
    for (let floorFigure of floorFigures) {
        if (floorFigure == targetFigure) {
            return true;
        }
    }
    return false;
}

function isInFrontAreaForPolygon(edge1: ZEdge, edge2: ZEdge, polygon: ZPolygon): boolean {
    let subVec: Vector3 = subVector(edge1.v0.pos, edge2.v1.pos);
    subVec.normalize();

    let isInFront: boolean = true;
    polygon.edges.forEach(edge => {
         let tempSubVec0: Vector3 = subVector(edge.v0.pos, edge1.v0.pos);
        let tempSubVec1: Vector3 = subVector(edge.v1.pos, edge1.v0.pos);
        if (subVec.dot(tempSubVec0) < 0 || subVec.dot(tempSubVec1) < 0) {
            isInFront = false;
        }

    });
    return isInFront;
}

function isInFrontAreaForEdge(edge1: ZEdge, edge2: ZEdge, edge: ZEdge): boolean {
    let subVec: Vector3 = subVector(edge1.v0.pos, edge2.v1.pos);
    subVec.normalize();

    let isInFront: boolean = true;
    let tempSubVec0: Vector3 = subVector(edge.v0.pos, edge1.v0.pos).normalize();
    let tempSubVec1: Vector3 = subVector(edge.v1.pos, edge1.v0.pos).normalize();
    if (subVec.dot(tempSubVec0) < 0 || subVec.dot(tempSubVec1) < 0) {
        isInFront = false;
    };

    return isInFront;
}

function isInFrontEdgeDirForEdge(edge: ZEdge, nor: Vector3, otherEdge: ZEdge): boolean
{
    let isInFront: boolean = true;
    let tempSubVec0: Vector3 = subVector(otherEdge.v0.pos, edge.v0.pos).normalize();
    let tempSubVec1: Vector3 = subVector(otherEdge.v1.pos, edge.v0.pos).normalize();
    if (nor.clone().dot(tempSubVec0) < 0 || nor.clone().dot(tempSubVec1) < 0) {
        isInFront = false;
    };

    return isInFront;
}

function getFirstRect(figures: TFigureElement[], isX: boolean): ZRect {
    let xMin: number = figures[0].rect.rect_center.x;
    let yMin: number = figures[0].rect.rect_center.y;
    let firstRect: ZRect = figures[0].rect;
    for (let figure of figures) {
        if (isX) {
            if (xMin < figure.rect.rect_center.x) {
                xMin = figure.rect.rect_center.x;
                firstRect = figure.rect;
            }
        }
        else {
            if (yMin < figure.rect.rect_center.y) {
                yMin = figure.rect.rect_center.y;
                firstRect = figure.rect;
            }
        }
    }
    return firstRect;
}

function hasSameDirectionAndPositon(recordDirMap: Map<Vector3, TFigureElement[]>, dir: Vector3, point: Vector3): Vector3 {
    dir.normalize();
    for (let entry of recordDirMap.entries()) {
        if (isSameDirectionAndPosition(entry[0], dir, entry[1][0].rect.rect_center, point)) {
            return entry[0];
        }
    }
    return null;
}

function isSameDirectionAndPosition(dir1: Vector3, dir2: Vector3, point1: Vector3, point2: Vector3, tol: number = 0.05): boolean {
    let state1: boolean = isSameDirection(dir1, dir2, tol);
    let pointDir: Vector3 = point1.sub(point2);
    pointDir.normalize();
    let state2: boolean = 1 - Math.abs(pointDir.dot(dir1)) < tol;
    return state1 && state2;
}


function isSameDirection(dir1: Vector3, dir2: Vector3, tol: number = 0.15): boolean {
    dir1.normalize();
    dir2.normalize();
    return 1 - Math.abs(dir1.dot(dir2)) < tol;
}

function getAllIShape(figures: TFigureElement[]): TFigureElement[][] {
    let figureNum: number = figures.length;
    let allIFigures: TFigureElement[][] = [];
    for (let i = 0; i < figureNum; ++i) {
        let isExist: boolean = false;
        for (let figures of allIFigures) {
            if (figures.indexOf(figures[i]) != -1) {
                isExist = true;
                continue
            }
        }
        if (isExist) {
            continue;
        }

        // TODO 这里重新更改IShape，确保其确定一字型是ok的
        let dir: Vector3 = figures[i].rect.backEdge.dv;
        let pos: Vector3 = figures[i].rect.rect_center;
        let IFigures: TFigureElement[] = [figures[i]];
        for (let j = 0; j < figureNum; ++j) {
            if (i == j) {
                continue;
            }
            if (isSameDirByRectFrontAndBackEdge(figures[j].rect, dir) && isNearByPosAndDir(dir, pos, figures[j].rect.rect_center)) {
                IFigures.push(figures[j]);
            }
        }
        if (IFigures.length > 1) {
            allIFigures.push(IFigures);
        }
    }

    for (let entry of allIFigures) {
        TBaseRoomToolUtil.instance.sortFigureElementsByCenter(entry);
    }

    return allIFigures;
}


function isNearByPosAndDir(dir: Vector3, pos: Vector3, pos1: Vector3, tol = 0.01) {
    let subVec: Vector3 = pos.clone().sub(pos1).normalize();
    return 1 - Math.abs(subVec.dot(dir)) < tol;
}

function isSameDirByRectFrontAndBackEdge(rect: ZRect, dir: Vector3, tol: number = 0.01): boolean {
    return 1 - Math.abs(rect.backEdge.dv.clone().dot(dir)) < tol;
}

function isNearRectsByFigures(figures: TFigureElement[], baseFigure: TFigureElement) {
    for (let figure of figures) {
        if (TBaseRoomToolUtil.instance.isLayonLeftOrRight(figure.rect, baseFigure.rect)) {
            return true;
        }
    }
    return false;
}


function findNearLength(lengths: number[], dis: number, tol: number = 0.01): boolean {
    for (let length of lengths) {
        if (Math.abs(length - dis) < tol) {
            return true;
        }
    }
    return false;
}

function calEdgePair(polygon: ZPolygon): ZEdge[][] {
    let targetEdges: ZEdge[][] = [];
    let edges: ZEdge[] = polygon.edges;
    let edgeNum: number = edges.length;
    for (let i = 0; i < edgeNum; ++i) {
        let leftDisInfo: Map<number, ZEdge> = new Map<number, ZEdge>();
        let rightDisInfo: Map<number, ZEdge> = new Map<number, ZEdge>();
        for (let j = 0; j < edgeNum; ++j) {
            if (i == j || !isInRangeAndParallel(edges[i], edges[j])) {
                continue;
            }
            let flag: number = edges[i].nor.dot(subVector(edges[j].v0.pos, edges[i].v0.pos));
            let dis: number = TBaseRoomToolUtil.instance.calDistance(edges[i], edges[j]);
            if (flag < 0) {
                leftDisInfo.set(dis, edges[j]);
            }
            else if (flag > 0) {
                rightDisInfo.set(dis, edges[j]);
            }
        }
        let leftMinDis: number = Number.POSITIVE_INFINITY;
        let rightMinDis: number = Number.POSITIVE_INFINITY;
        let leftMinEdge: ZEdge = null;
        let rightMinEdge: ZEdge = null;
        if (leftDisInfo.size > 0) {
            for (let entry of leftDisInfo.entries()) {
                // TODO 需要进行筛选 
                if (leftMinDis > entry[0] && isCrossByV0V1(edges[i], entry[1])) {
                    leftMinDis = entry[0];
                    leftMinEdge = entry[1];
                }
            }
        }
        if (rightDisInfo.size > 0) {
            for (let entry of rightDisInfo.entries()) {
                if (rightMinDis > entry[0] && isCrossByV0V1(edges[i], entry[1])) {
                    rightMinDis = entry[0];
                    rightMinEdge = entry[1];
                }
            }
        }
        // TODO 这里再加个判断确定是取左侧还是右侧
        let minEdge: ZEdge = null;
        if (leftMinEdge && rightMinEdge) {
            minEdge = getRealPairEdge(edges, edges[i], leftMinEdge, rightMinEdge);
        }
        else if (leftMinEdge) {
            minEdge = leftMinEdge;
        }
        else if (rightMinEdge) {
            minEdge = rightMinEdge;
        }


        if (minEdge) {
            targetEdges.push([edges[i], minEdge]);
        }
    }
    return targetEdges;
}

// 抽象下接口
function isCrossByV0V1(edge1: ZEdge, edge2: ZEdge): boolean {
    let crossEdge0: ZEdge = new ZEdge(edge1.v0, edge2.v0);
    crossEdge0.computeNormal();
    let crossEdge1: ZEdge = new ZEdge(edge1.v1, edge2.v1);
    crossEdge1.computeNormal();

    return TBaseRoomToolUtil.instance.isCrossByVertex(crossEdge0.v0.pos, crossEdge0.v1.pos, crossEdge1.v0.pos, crossEdge1.v1.pos);
}

function getCrossByV0V1(edge1: ZEdge, edge2: ZEdge): Vector3 {
    let crossEdge0: ZEdge = new ZEdge(edge1.v0, edge2.v0);
    crossEdge0.computeNormal();
    let crossEdge1: ZEdge = new ZEdge(edge1.v1, edge2.v1);
    crossEdge1.computeNormal();
    let crossPoint: Vector3 = null;
    // TODO 下面计算交叉点有问题，先跳过计算，确认是有相交点
    if (crossPoint = crossEdge0.getIntersection(crossEdge1)) {
        return crossPoint;
    }
    return null;
}

function getRealPairEdge(edges: ZEdge[], baseEdge: ZEdge, minEdge1: ZEdge, minEdge2: ZEdge) {
    let isCross1: boolean = false;
    let crossPoint1: Vector3 = getCrossByV0V1(baseEdge, minEdge1);
    if (crossPoint1) {
        isCross1 = true;
    }

    let isCross2: boolean = false;
    let crossPoint2: Vector3 = getCrossByV0V1(baseEdge, minEdge2);
    if (crossPoint2) {
        isCross2 = true;
    }
    if (isCross1 && isCross2) {
        let zDir: Vector3 = new Vector3(0, 0, 1);
        // TODO 通过向量的叉乘方向确定此交叉点实在多边形内外
        let firstFlag: boolean = null;
        for (let edge of edges) {
            let subVec1: Vector3 = subVector(edge.v0.pos, crossPoint1);
            let subVec2: Vector3 = subVector(edge.v1.pos, crossPoint1);
            let crossVec: Vector3 = subVec1.cross(subVec2);
            let dotFlag = crossVec.dot(zDir);
            if (firstFlag == null) {
                firstFlag = dotFlag > 0;
            }
            else {
                if (dotFlag > 0 != firstFlag) {
                    return minEdge2;
                }
            }
        }
        return minEdge1;
    }
    else if (isCross1) {
        return minEdge1;
    }
    else if (isCross2) {
        return minEdge2;
    }
}

function subVector(vec1: Vector3, vec2: Vector3) {
    let subVect: Vector3 = new Vector3(vec1.x - vec2.x, vec1.y - vec2.y, vec1.z - vec2.z);
    return subVect;
}

function isInRangeAndParallel(baseEdge: ZEdge, edge: ZEdge, tol: number = 0.01): boolean {
    // TODO 确定edge是否有交叉
    let xBaseMin: number = Math.min(baseEdge.v0.pos.x, baseEdge.v1.pos.x);
    let xBaseMax: number = Math.max(baseEdge.v0.pos.x, baseEdge.v1.pos.x);
    let yBaseMin: number = Math.min(baseEdge.v0.pos.y, baseEdge.v1.pos.y);
    let yBaseMax: number = Math.max(baseEdge.v0.pos.y, baseEdge.v1.pos.y);

    let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
    let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
    let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
    let yMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);

    if ((((xBaseMax >= xMin) && (xBaseMin <= xMax)) || ((yBaseMax >= yMin) && (yBaseMin <= yMax)))
        && 1 - Math.abs(baseEdge.dv.dot(edge.dv)) < tol) {
        return true;
    }

    return false;
}

function calMinDistanByEdgePair(edgePairs: ZEdge[][]): any {
    let minDis: number = Number.POSITIVE_INFINITY;
    let minEdgePair: ZEdge[] = null;
    for (let pairIndex = 0; pairIndex < edgePairs.length; ++pairIndex) {
        let dis: number = TBaseRoomToolUtil.instance.calDistance(edgePairs[pairIndex][0], edgePairs[pairIndex][1]);
        if (dis < minDis) {
            minDis = dis;
            minEdgePair = edgePairs[pairIndex];
        }
    }
    return { minDis: minDis, minEdgePair: minEdgePair };
}

function getBoundingBox(figure: TFigureElement, isMatchRect: boolean = false): any {
    // TODO 获取3d包围框
    let xMin: number = Number.POSITIVE_INFINITY;
    let xMax: number = Number.NEGATIVE_INFINITY;
    let yMin: number = Number.POSITIVE_INFINITY;
    let yMax: number = Number.NEGATIVE_INFINITY;
    let zMin: number = figure.rect.zval;
    let zMax: number = figure.rect.zval + figure.height;

    let rect: ZRect = isMatchRect ? figure.matched_rect || figure.rect : figure.rect;

    for (let edge of rect.edges) {
        let edgeXMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
        let edgeXMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
        let edgeYMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
        let edgeYMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);

        if (edgeXMin < xMin) {
            xMin = edgeXMin;
        }
        if (edgeXMax > xMax) {
            xMax = edgeXMax;
        }
        if (edgeYMin < yMin) {
            yMin = edgeYMin;
        }
        if (edgeYMax > yMax) {
            yMax = edgeYMax;
        }
    }

    return { xMin: xMin, xMax: xMax, yMin: yMin, yMax: yMax, zMin: zMin, zMax: zMax };
}


function isNearEdgeLengthMoreThanTol(edgePair: ZEdge[], edges: ZEdge[], tol: number = 50): boolean {
    // 1. 找出边对的邻边
    let nearEdges: ZEdge[] = [];
    for (let edge of edges) {
        if (isNearEdge(edge, edgePair)) {
            nearEdges.push(edge);
        }
    }
    let nearNearEdges: ZEdge[] = [];
    for (let edge of edges) {
        if (isNearEdge(edge, nearEdges)) {
            nearNearEdges.push(edge);
        }
    }

    let minEdgeLen: number = Number.POSITIVE_INFINITY;
    nearNearEdges.forEach(edge => {
        minEdgeLen = Math.min(edge.length, minEdgeLen);
    });
    if (minEdgeLen <= tol) {
        return false
    }
    return true;
}

function isNearEdge(edge: ZEdge, edgePair: ZEdge[]): boolean {
    for (let tempEdge of edgePair) {
        if (tempEdge.v0.pos.equals(edge.v0.pos) || tempEdge.v0.pos.equals(edge.v1.pos)
            || tempEdge.v1.pos.equals(edge.v0.pos) || tempEdge.v1.pos.equals(edge.v1.pos)) {
            return true;
        }
    }
    return false;
}

function calCommonLenByEdgePair(edgePair: ZEdge[]): number {
    let xMinEdge1: number = Math.min(edgePair[0].v0.pos.x, edgePair[0].v1.pos.x);
    let xMaxEdge1: number = Math.max(edgePair[0].v0.pos.x, edgePair[0].v1.pos.x);
    let yMinEdge1: number = Math.min(edgePair[0].v0.pos.y, edgePair[0].v1.pos.y);
    let yMaxEdge1: number = Math.max(edgePair[0].v0.pos.y, edgePair[0].v1.pos.y);

    let xMinEdge2: number = Math.min(edgePair[1].v0.pos.x, edgePair[1].v1.pos.x);
    let xMaxEdge2: number = Math.max(edgePair[1].v0.pos.x, edgePair[1].v1.pos.x);
    let yMinEdge2: number = Math.min(edgePair[1].v0.pos.y, edgePair[1].v1.pos.y);
    let yMaxEdge2: number = Math.max(edgePair[1].v0.pos.y, edgePair[1].v1.pos.y);

    if (xMinEdge1 < xMaxEdge2 && xMaxEdge1 > xMinEdge2) {
        let xRangeMin: number = Math.max(xMinEdge1, xMinEdge2);
        let xRangeMax: number = Math.min(xMaxEdge1, xMaxEdge2);
        return (xRangeMax - xRangeMin) / Math.max(edgePair[0].length, edgePair[1].length);
    }

    if (yMinEdge1 < yMaxEdge2 && yMaxEdge1 > yMinEdge2) {
        let yRangeMin: number = Math.max(yMinEdge1, yMinEdge2);
        let yRangeMax: number = Math.min(yMaxEdge1, yMaxEdge2);
        return (yRangeMax - yRangeMin) / Math.max(edgePair[0].length, edgePair[1].length);
    }
    return 0;
}

function getOtherRoomEdges(room: TRoom, figure: TFigureElement, layonInfo: any): ZEdge[] {
    let roomEdges: ZEdge[] = layonInfo.roomEdges;
    let otherRoomEdges: ZEdge[] = [];
    for (let edge of room.room_shape._poly.edges) {
        let isNear: boolean = false;
        for (let roomEdge of roomEdges) {
            if (edge.v0.pos.equals(roomEdge.v0.pos) || edge.v0.pos.equals(roomEdge.v1.pos) || edge.v1.pos.equals(roomEdge.v0.pos) || edge.v1.pos.equals(roomEdge.v1.pos)) {
                isNear = true;
            }
            for (let edgeType of layonInfo.figureEdgeTypes) {
                switch (edgeType) {
                    case RectEdgeType.k_back:
                        {
                            if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(figure.rect.backEdge, figure.rect.frontEdge, edge)) {
                                isNear = true;
                            }
                            break;
                        }
                    case RectEdgeType.k_front:
                        {
                            if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(figure.rect.frontEdge, figure.rect.backEdge, edge)) {
                                isNear = true;
                            }
                            break;
                        }
                    case RectEdgeType.k_left:
                        {
                            if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(figure.rect.leftEdge, figure.rect.rightEdge, edge)) {
                                isNear = true;
                            }
                            break;
                        }
                    case RectEdgeType.k_right:
                        {
                            if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(figure.rect.rightEdge, figure.rect.leftEdge, edge)) {
                                isNear = true;
                            }
                            break;
                        }
                    default:
                        break;
                }
            }
        }
        if (!isNear) {
            otherRoomEdges.push(edge);
        }
    }
    return otherRoomEdges;
}

function getOtherFiguresByRoomEdges(figures: TFigureElement[], roomEdges: ZEdge[], room: TRoom): TFigureElement[] {
    let otherFigures: TFigureElement[] = [];
    for (let figure of figures) {
        let isTarget: boolean = false;
        for (let roomEdge of roomEdges) {
            if (getTypeEdgeLayonRect(roomEdge, figure.rect)) {
                isTarget = true;
            }
        }
        if (isTarget) {
            otherFigures.push(figure);
        }
    }
    let hangFigures: TFigureElement[] = [];
    figures.forEach(figure => {
        let isLayonRoomEdge: boolean = false;
        for (let roomEdge of room.room_shape._poly.edges) {
            if (getTypeEdgeLayonRect(roomEdge, figure.rect)) {
                isLayonRoomEdge = true;
                break;
            }
        }
        if (!isLayonRoomEdge) {
            hangFigures.push(figure);
        }
    });
    otherFigures = [...otherFigures, ...hangFigures];
    return otherFigures;
}

function getTypeEdgeLayonRect(edge: ZEdge, rect: ZRect): RectEdgeType {
    let edgeType: RectEdgeType = null;
    for (let rectEdge of rect.edges) {
        if (edge.islayOn(rectEdge, 5, 0.01)) {
            edgeType = TBaseRoomToolUtil.instance.getEdgeTypeFromRect(rect, rectEdge);
            break;
        }
    }
    return edgeType;
}

function getWallNearDoorFigureDist(room: TRoom, figures: TFigureElement[]): number {
    let doors: I_Window[] = room.windows.filter(window => window.type == "Door");
    let doorWallInfos: any = [];
    for (let wallEdge of room.room_shape._poly.edges) {
        for (let door of doors) {
            if (getTypeEdgeLayonRect(wallEdge, door.rect)) {
                doorWallInfos.push({ door: door, wallEdge: wallEdge });
            }
        }
    }
    doorWallInfos = Array.from(new Set(doorWallInfos));
    let minDist: number = Number.POSITIVE_INFINITY;
    for (let doorWallInfo of doorWallInfos) {
        let dist: number = getNearDoorFigureMinDist(room, figures, doorWallInfo);
        if (minDist > dist) {
            minDist = dist;
        }
    }
    return minDist;
}

function getNearDoorFigureMinDist(room: TRoom, figures: TFigureElement[], doorWallInfo: any): number {
    let nearWallFigures: TFigureElement[] = [];
    let figureLayInfo1: any = null;
    let figureLayInfo2: any = null;
    let dist1: number = Number.POSITIVE_INFINITY;
    let dist2: number = Number.POSITIVE_INFINITY;
    let doorDir: Vector3 = doorWallInfo.door.rect.backEdge.dv;
    let doorCenter: Vector3 = doorWallInfo.door.rect.rect_center;
    for (let figure of figures) {
        let layonFigureRectType: RectEdgeType = getTypeEdgeLayonRect(doorWallInfo.wallEdge, figure.rect);
        if (layonFigureRectType) {
            nearWallFigures.push(figure);
            let figureCenter: Vector3 = figure.rect.rect_center;
            let subVec: Vector3 = doorCenter.clone().sub(figureCenter);
            let dist: number = subVec.clone().dot(doorDir);
            if (dist >= 0) {
                if (dist1 > Math.abs(dist)) {
                    dist1 = Math.abs(dist);
                    let nearDoorRectType: RectEdgeType = getNearDoorRectType(doorCenter, figure.rect, layonFigureRectType);
                    figureLayInfo1 = { figure: figure, layonRectType: nearDoorRectType };
                }
            }
            else {
                if (dist2 > Math.abs(dist)) {
                    dist2 = Math.abs(dist);
                    let nearDoorRectType: RectEdgeType = getNearDoorRectType(doorCenter, figure.rect, layonFigureRectType);
                    figureLayInfo2 = { figure: figure, layonRectType: nearDoorRectType };
                }
            }
        }
    }
    let dist: number = Number.POSITIVE_INFINITY;
    if (figureLayInfo1 && figureLayInfo2) {
        dist = TBaseRoomToolUtil.instance.calDistanceByPolygons(figureLayInfo1.figure.rect, figureLayInfo2.figure.rect);
    }
    else if (figureLayInfo1) {
        dist = calRoomDisByFigure(room, figureLayInfo1);
    }
    else if (figureLayInfo2) {
        dist = calRoomDisByFigure(room, figureLayInfo2);
    }
    return dist;
}

function calRoomDisByFigure(room: TRoom, figureLayInfo: any): number {
    let rectEdge1: ZEdge = null;
    let rectEdge2: ZEdge = null;
    switch (figureLayInfo.layonRectType) {
        case RectEdgeType.k_back:
            {
                rectEdge1 = figureLayInfo.figure.rect.backEdge;
                rectEdge2 = figureLayInfo.figure.rect.frontEdge;
                break;
            }
        case RectEdgeType.k_front:
            {
                rectEdge1 = figureLayInfo.figure.rect.frontEdge;
                rectEdge2 = figureLayInfo.figure.rect.backEdge;
                break;
            }
        case RectEdgeType.k_left:
            {
                rectEdge1 = figureLayInfo.figure.rect.leftEdge;
                rectEdge2 = figureLayInfo.figure.rect.rightEdge;
                break;
            }
        case RectEdgeType.k_right:
            {
                rectEdge1 = figureLayInfo.figure.rect.rightEdge;
                rectEdge2 = figureLayInfo.figure.rect.leftEdge;
                break;
            }
        default:
            break;
    }
    let minDis: number = Number.POSITIVE_INFINITY;
    for (let roomEdge of room.room_shape._poly.edges) {
        if (TBaseRoomToolUtil.instance.edgeIsInFrontArea(rectEdge1, rectEdge2, roomEdge)) {
            let dist: number = TBaseRoomToolUtil.instance.calDistance(rectEdge1, roomEdge);
            if (minDis > dist) {
                minDis = dist;
            }
        }
    }
    return minDis;
}

function getNearDoorRectType(doorCenter: Vector3, rect: ZRect, layonRectType: RectEdgeType): RectEdgeType {
    let nearDoorRectType: RectEdgeType = null;
    switch (layonRectType) {
        case RectEdgeType.k_back:
        case RectEdgeType.k_front:
            {
                let leftCenter: Vector3 = rect.leftEdge.center;
                let rightCenter: Vector3 = rect.rightEdge.center;
                let leftDist: number = leftCenter.clone().sub(doorCenter).length();
                let rightDist: number = rightCenter.clone().sub(doorCenter).length();
                nearDoorRectType = leftDist <= rightDist ? RectEdgeType.k_left : RectEdgeType.k_right;
                break;
            }
        case RectEdgeType.k_left:
        case RectEdgeType.k_right:
            {
                let backCenter: Vector3 = rect.backEdge.center;
                let frontCenter: Vector3 = rect.frontEdge.center;
                let backDist: number = backCenter.clone().sub(doorCenter).length();
                let frontDist: number = frontCenter.clone().sub(doorCenter).length();
                nearDoorRectType = backDist <= frontDist ? RectEdgeType.k_back : RectEdgeType.k_front;
                break;
            }
        default:
            break;
    }
    return nearDoorRectType;
}

function calRatioRangeByEdgeAndRect(edge: ZEdge, rect: ZRect): number {
    let xMin: number = Math.min(edge.v0.pos.x, edge.v1.pos.x);
    let xMax: number = Math.max(edge.v0.pos.x, edge.v1.pos.x);
    let yMin: number = Math.min(edge.v0.pos.y, edge.v1.pos.y);
    let yMax: number = Math.max(edge.v0.pos.y, edge.v1.pos.y);
    let ratio: number = 0;
    let rectBox: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
    if (xMin < rectBox.xMax && xMax > rectBox.xMin) {
        let xMinRange: number = Math.max(xMin, rectBox.xMin);
        let xMaxRange: number = Math.min(xMax, rectBox.xMax);
        ratio = (xMaxRange - xMinRange) / Math.min(xMax - xMin, rectBox.xMax - rectBox.xMin);
        if (ratio == 1) {
            let dist1: number = Math.max(xMin, rectBox.xMin) - Math.min(xMin, rectBox.xMin);
            let dist2: number = Math.max(xMax, rectBox.xMax) - Math.min(xMax, rectBox.xMax);
            ratio = 1 + Math.min(dist1, dist2) / Math.max(xMax - xMin, rectBox.xMax - rectBox.xMin);
        }
    }

    if (yMin < rectBox.yMax && yMax > rectBox.yMin) {
        let yMinRange: number = Math.max(yMin, rectBox.yMin);
        let yMaxRange: number = Math.min(yMax, rectBox.yMax);
        ratio = (yMaxRange - yMinRange) / Math.min(yMax - yMin, rectBox.yMax - rectBox.yMin);
        if (ratio == 1) {
            let dist1: number = Math.max(yMin, rectBox.yMin) - Math.min(yMin, rectBox.yMin);
            let dist2: number = Math.max(yMax, rectBox.yMax) - Math.min(yMax, rectBox.yMax);
            ratio = 1 + Math.min(dist1, dist2) / Math.max(yMax - yMin, rectBox.yMax - rectBox.yMin);
        }
    }
    return ratio;
}

function calDistanceByParallelEdges(edge1: ZEdge, edge2: ZEdge): number {
    let dv1: Vector3 = edge1.dv;
    let dv2: Vector3 = edge2.dv;
    if (dv1.dot(dv2) < 0.9) {
        return null;
    }
    return TBaseRoomToolUtil.instance.calDistance(edge1, edge2);
}

function getRoomDoorDirs(room: TRoom): Vector3[] {
    let doors: I_Window[] = room.windows.filter(window => window.type == "Door");
    let doorDirs: Vector3[] = [];
    doors.forEach(door => {
        doorDirs.push(door.rect.backEdge.dv);
    });
    return doorDirs;
}

// 用包围盒进行简单地计算
function isMainFlowForFigures(doorDirVecs: Vector3[], figure1: TFigureElement, figure2: TFigureElement, room: TRoom, otherFigures: TFigureElement[]) {
    let figureCenter1: Vector3 = figure1.rect.rect_center;
    let figureCenter2: Vector3 = figure2.rect.rect_center;
    let subFigureVec: Vector3 = figureCenter1.clone().sub(figureCenter2).normalize();
    let isMainFlow: boolean = false;
    for (let doorDir of doorDirVecs) {
        if (Math.abs(subFigureVec.dot(doorDir)) > 0.5 && (Math.abs(figure1.rect.frontEdge.dv.clone().dot(figure2.rect.frontEdge.dv)) > 0.9) && (
            TBaseRoomToolUtil.instance.polygonIsInFrontArea(figure1.rect.frontEdge, figure1.rect.backEdge, figure2.rect) &&
            TBaseRoomToolUtil.instance.polygonIsInFrontArea(figure2.rect.frontEdge, figure2.rect.backEdge, figure1.rect))) {
            isMainFlow = true;
        }
    }

    if (isMainFlow) {
        // 只能处理一个门,有点不明白这里使用的原因
        let doors: I_Window[] = room.windows.filter(window => window.type == "Door")
        if (doors.length) {
            for (let door of doors) {
                let checkOtherFigures: TFigureElement[] = TBaseRoomToolUtil.instance.getOtherFigures(figure2, otherFigures);
                if (checkOtherFigures.length) {
                    let otherFigureDist: number = getFigureWithDoorNorDist(checkOtherFigures, door);
                    let targetFigureDist: any = getFigureWithDoorNorDist([figure1, figure2], door);
                    // 判断是否包围
                    if (targetFigureDist > otherFigureDist) {
                        isMainFlow = false;
                    }
                }
            }
        }
    }
    return isMainFlow;
}

function getFigureWithDoorNorDist(figures: TFigureElement[], door: I_Window,): number {
    let doorNor: Vector3 = door.rect.backEdge.nor;
    let dist: number = Number.NEGATIVE_INFINITY;
    for (let figure of figures) {
        let figureCenter: Vector3 = figure.rect.rect_center.clone();
        let projectDist: number = Math.abs(figureCenter.dot(doorNor));
        if (dist < projectDist) {
            dist = projectDist;
        }
    }
    return dist;
}

function isUseLayonWindow(window: I_Window, figure: TFigureElement, isMatchedRect: boolean = false): boolean {
    let useLayon: boolean = true;
    if (window.realType == "SingleDoor") {
        let windowNor: Vector3 = window.rect.backEdge.nor;
        //figure比较靠近back
        let figureRect: ZRect = isMatchedRect ? figure.matched_rect || figure.rect : figure.rect;
        let figureCenter: Vector3 = figureRect.rect_center;
        let windwoBackDist: number = Math.abs(figureCenter.clone().sub(window.rect.backEdge.v0.pos).dot(windowNor));
        let windowFrontDist: number = Math.abs(figureCenter.clone().sub(window.rect.frontEdge.v0.pos).dot(windowNor));
        if (windwoBackDist < windowFrontDist) {
            useLayon = false;
        }
    }
    return useLayon;
}

function isOverlayWithDoor(doorStartPoint: Vector3, doorDir: Vector3, doorNor: Vector3, doorLen: number, figure: TFigureElement, isMatchedRect: boolean = false): boolean {
    let figurePoints: Vector3[] = [];
    let figureRect: ZRect = isMatchedRect ? figure.matched_rect || figure.rect : figure.rect;
    figureRect.vertices.forEach(vertex => { figurePoints.push(vertex.pos) });
    let flag1: boolean = null;
    let flag2: boolean = null;
    figurePoints.forEach(point => {
        if (point.clone().sub(doorStartPoint).dot(doorDir) <= 0) {
            flag1 = true;
        }
        else {
            flag2 = true;
        }
    });
    // 通过这些状态位进行判断
    if (flag1 && flag2) {
        for (let point of figurePoints) {
            let doorAndFigureDis: number = Math.abs(point.clone().sub(doorStartPoint).dot(doorNor));
            if (doorAndFigureDis <= doorLen) {
                return true;
            }
        }
    }
    else if (flag2) {
        let doorAndFigureDis: number = Number.POSITIVE_INFINITY;
        for (let point of figurePoints) {
            let dist: number = doorStartPoint.distanceTo(point);
            if (dist < doorAndFigureDis) {
                doorAndFigureDis = dist;
            }
        }
        if (doorAndFigureDis <= doorLen) {
            return true;
        }
    }
    return false;
}

function ratioOfFigureOverRoom(room: TRoom, figure: TFigureElement, overLayWallTol: number, isMatchRect: boolean = false): number {
    let overRoomRatio: number = 0;
    for (let roomEdge of room.room_shape._poly.edges) {
        if (TBaseRoomToolUtil.instance.isEdgeOverLayPolygon(roomEdge, isMatchRect ? figure.matched_rect || figure.rect : figure.rect, 0, true)) {
            overRoomRatio += calRatioFigureLeftOrRightOverRoom(room, roomEdge, figure, overLayWallTol, isMatchRect);
            overRoomRatio += calRatioFigureBackOrFrontOverRoom(room, roomEdge, figure, overLayWallTol, isMatchRect);
        }
    }
    return overRoomRatio;
}

function calRatioFigureLeftOrRightOverRoom(room: TRoom, sourceRoomEdge: ZEdge, figure: TFigureElement, overLayWallTol: number, isMatchRect: boolean = false): number {
    let rect: ZRect = isMatchRect ? figure.matched_rect || figure.rect : figure.rect;
    let rectLeftEdge: ZEdge = rect.leftEdge;
    let rectRightEdge: ZEdge = rect.rightEdge;
    if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(sourceRoomEdge, rectLeftEdge))
    {
        return 0;
    }
    let roomEdgeNor: Vector3 = sourceRoomEdge.nor.clone();
    let roomEdgeCenter: Vector3 = sourceRoomEdge.center.clone();
    let leftCenter: Vector3 = rectLeftEdge.center.clone();
    let rightCenter: Vector3 = rectRightEdge.center.clone();
    let subLeft: Vector3 = leftCenter.clone().sub(roomEdgeCenter);
    let subRight: Vector3 = rightCenter.clone().sub(roomEdgeCenter);
    let leftDot: number = subLeft.clone().dot(roomEdgeNor);
    let rightDot: number = subRight.clone().dot(roomEdgeNor);
    let ratio: number = 0;
    if(leftDot > 0)
    {
        let overLayLen: number = TBaseRoomToolUtil.instance.calDistance(sourceRoomEdge, rectLeftEdge);
        if (overLayLen > overLayWallTol) {
            ratio += (1 + overLayLen / rect.backEdge.length);
        }
    }
    if(rightDot > 0)
    {
        let overLayLen: number = TBaseRoomToolUtil.instance.calDistance(sourceRoomEdge, rectRightEdge);
        if (overLayLen > overLayWallTol) {
            ratio += (1 + overLayLen / rect.backEdge.length);
        }
    }
    return ratio;
}

function calRatioFigureBackOrFrontOverRoom(room: TRoom, sourceRoomEdge: ZEdge, figure: TFigureElement, overLayWallTol: number, isMatchRect: boolean = false): number {
    let rect: ZRect = isMatchRect ? figure.matched_rect || figure.rect : figure.rect;
    let rectFrontEdge: ZEdge = rect.frontEdge;
    let rectBackEdge: ZEdge = rect.backEdge;
    if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(sourceRoomEdge, rectFrontEdge))
    {
        return 0;
    }
    let roomEdgeNor: Vector3 = sourceRoomEdge.nor.clone();
    let roomEdgeCenter: Vector3 = sourceRoomEdge.center.clone();
    let frontCenter: Vector3 = rectFrontEdge.center.clone();
    let backCenter: Vector3 = rectBackEdge.center.clone();
    let subFront: Vector3 = frontCenter.clone().sub(roomEdgeCenter);
    let subBack: Vector3 = backCenter.clone().sub(roomEdgeCenter);
    let frontDot: number = subFront.clone().dot(roomEdgeNor);
    let backDot: number = subBack.clone().dot(roomEdgeNor);
    let ratio: number = 0;
    if(frontDot > 0)
    {
        let overLayLen: number = TBaseRoomToolUtil.instance.calDistance(sourceRoomEdge, rectFrontEdge);
        if (overLayLen > overLayWallTol) {
            ratio += (1 + overLayLen / rect.leftEdge.length);
        }
    }
    if(backDot > 0)
    {
        let overLayLen: number = TBaseRoomToolUtil.instance.calDistance(sourceRoomEdge, rectBackEdge);
        if (overLayLen > overLayWallTol) {
            ratio += (1 + overLayLen / rect.leftEdge.length);
        }
    }
    return ratio;
}

// 收集指定指标有问题的图元，只筛选干涉这块的图元
function getAbnormalFigures(layoutScores: I_LayoutScore[], layoutRuleNames: string[] = null): TFigureElement[] {
    let abnormalFigures: TFigureElement[] = [];
    for (let layoutScore of layoutScores) {
        if (!layoutScore?.children) {
            if (layoutScore.score == -100 && (layoutRuleNames ? layoutRuleNames.includes(layoutScore.name) : true)) {
                if (layoutScore.fineTuningFigures) {
                    abnormalFigures.push(...layoutScore.fineTuningFigures);
                }
            }
        }
        else {
            for (let subLayoutScore of layoutScore.children) {
                if (subLayoutScore.score == -100 && (layoutRuleNames ? layoutRuleNames.includes(subLayoutScore.name) : true)) {
                    if (subLayoutScore.fineTuningFigures) {
                        abnormalFigures.push(...subLayoutScore.fineTuningFigures);
                    }
                }
            }
        }
    }
    return abnormalFigures;
}

function isSameFigure(sourceFigure: TFigureElement, abnormalFigures: TFigureElement[]): boolean {
    for (let abnormalFigure of abnormalFigures) {
        if (abnormalFigure.sub_category == sourceFigure.sub_category &&
            abnormalFigure.rect.is_shape_equal_to(sourceFigure.rect)) {
            return true;
        }
    }
    return false;
}

function calDistanceByFigurePoint(figure1: TFigureElement, figure2: TFigureElement): number {
    let minDis: number = Number.POSITIVE_INFINITY;
    for (let point1 of figure1.rect.vertices) {
        for (let point2 of figure2.rect.vertices) {
            let pointDis: number = point1.pos.distanceTo(point2.pos);
            if (pointDis < minDis) {
                minDis = pointDis;
            }
        }
    }
    return minDis;
}

function isSameFigureGroup(figure1: TFigureElement, figure2: TFigureElement): boolean {
    return figure1._group_uuid == figure2._group_uuid
}

function updateOtherProjectRanges(otherProjectRanges: any, otherFigureProjectRange: any): any {
    let noIntersectRanges: any = [];
    let needUpdateRanges: any = [];
    for (let otherProjectRange of otherProjectRanges) {
        if (isIntersectByRange(otherProjectRange, otherFigureProjectRange)) {
            needUpdateRanges.push(otherProjectRange);
        }
        else {
            noIntersectRanges.push(otherProjectRange);
        }
    }

    if (needUpdateRanges.length) {
        needUpdateRanges.push(otherFigureProjectRange);
        let updateXMin: number = Number.POSITIVE_INFINITY;
        let updateXMax: number = Number.NEGATIVE_INFINITY;
        for (let needUpdateRange of needUpdateRanges) {
            if (updateXMin > needUpdateRange.xMin) {
                updateXMin = needUpdateRange.xMin;
            }
            if (updateXMax < needUpdateRange.xMax) {
                updateXMax = needUpdateRange.xMax;
            }
        }
        noIntersectRanges.push({ xMin: updateXMin, xMax: updateXMax });
    }
    else {
        noIntersectRanges.push(otherFigureProjectRange);
    }
    return noIntersectRanges;
}

function isIntersectByRange(range1: any, range2: any) {
    if (range1.xMin < range2.xMax && range1.xMax > range2.xMin) {
        return true;
    }
    return false;
}

function calDistanceFromFigureToDoor(doorFrontEdge: ZEdge, figure: TFigureElement): number {
    let subVec: Vector3 = figure.rect.rect_center.clone().sub(doorFrontEdge.v0.pos);
    return Math.abs(subVec.dot(doorFrontEdge.nor));
}

function calFigureProjectDoor(doorFrontEdge: ZEdge, figure: TFigureElement): any {
    let minLen: number = Number.POSITIVE_INFINITY;
    let maxLen: number = Number.NEGATIVE_INFINITY;
    for (let vertex of figure.rect.vertices) {
        let subVec: Vector3 = vertex.pos.clone().sub(doorFrontEdge.v0.pos);
        let len: number = subVec.dot(doorFrontEdge.dv);
        if (len < minLen) {
            minLen = len;
        }
        if (len > maxLen) {
            maxLen = len;
        }
    }
    let doorLength: number = doorFrontEdge.length;
    if (maxLen > 0 && minLen < doorLength) {
        let targetMinLen: number = minLen > 0 ? minLen : 0;
        let targetMaxLen: number = maxLen > doorLength ? doorLength : maxLen;
        return { xMin: targetMinLen, xMax: targetMaxLen };
    }
    return null;
}

function baseExpandRangeToWall(copySourceRange: any, room: TRoom, otherRects: ZRect[]): any
{
    let recordIndexs: number[] = [];
    while(true)
    {
        let rect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(copySourceRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(copySourceRange));
        let allMinDist: number = null;
        let allMinRange: any = null;
        let allModifyIndex: number = null;
        let allIsModifyXMin: boolean = false;
        let allIsModifyXMax: boolean = false;
        let allIsModifyYMin: boolean = false;
        let allIsModifyYMax: boolean = false;
        for(let i = 0; i < rect.edges.length; i++)
        {
            if(recordIndexs.includes(i))
            {
                continue;
            }
            let firstEdge: ZEdge = rect.edges[i];
            let secondEdge: ZEdge = rect.edges[(i + 2) % rect.edges.length];
            if(TBaseRoomToolUtil.instance.edgeOnPolygon(firstEdge, room.room_shape._poly, 10, 0.01))
            {
                recordIndexs.push(i);
                continue;
            }
            let xDot: number = firstEdge.nor.clone().dot(xDir);
            let yDot: number = firstEdge.nor.clone().dot(yDir);
            let isModifyXMin: boolean = false; let isModifyXMax: boolean = false;
            let isModifyYMin: boolean = false; let isModifyYMax: boolean = false;
            isModifyXMin = Math.abs(xDot) > 0.9 ? (xDot < -0.9 ? true : false): false;
            isModifyXMax = Math.abs(xDot) > 0.9 ? (xDot > 0.9 ? true : false): false;
            isModifyYMin = Math.abs(yDot) > 0.9 ? (yDot < -0.9 ? true : false): false;
            isModifyYMax = Math.abs(yDot) > 0.9 ? (yDot > 0.9 ? true : false): false;
            let minRange: any = null;
            let minDist: number = null;
            for(let roomEdge of room.room_shape._poly.edges)
            {
                if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(firstEdge, roomEdge) || !TBaseRoomToolUtil.instance.edgeIsInFrontArea(firstEdge, secondEdge, roomEdge))
                {
                    continue;
                }
                let roomDist: number = TBaseRoomToolUtil.instance.calDistance(firstEdge, roomEdge);
                let roomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(roomEdge);
                if(minDist == null)
                {
                    minDist = roomDist;
                    minRange = roomEdgeRange;
                }
                if(roomDist < minDist)
                {
                    minDist = roomDist;
                    minRange = roomEdgeRange;
                }
            }
            let firstOtherRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(firstEdge);
            for(let otherRect of otherRects)
            {
                let otherRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(otherRect);
                if(TBaseRoomToolUtil.instance.isOverlayRange2ds(firstOtherRange, otherRange, false))
                {
                    minDist = null;
                    minRange = null;
                    break;
                }
                if(!TBaseRoomToolUtil.instance.polygonIsInFrontArea(firstEdge, secondEdge, otherRect))
                {
                    continue;
                }
                let  otherDist: number = TBaseRoomToolUtil.instance.calDistanceBetweenEdgeAndPolygon(firstEdge, otherRect);
                if(minDist == null)
                {
                    minDist = otherDist;
                    minRange = otherRange;
                }
                if(otherDist < minDist)
                {
                    if(recordIndexs.length == 3)
                    {
                        minDist = otherDist;
                        minRange = otherRange;
                    }
                    else
                    {
                        minDist = null;
                        minRange = null;
                        break;
                    }
                }
            }
            if(minDist == null)
            {
                continue;
            }
            if(allMinDist == null)
            {
                allMinDist = minDist;
                allMinRange = minRange;
                allIsModifyXMin = isModifyXMin;
                allIsModifyXMax = isModifyXMax;
                allIsModifyYMin = isModifyYMin;
                allIsModifyYMax = isModifyYMax;
                allModifyIndex = i;
            }
            if(allMinDist != null && allMinDist > minDist)
            {
                allMinDist = minDist;
                allMinRange = minRange;
                allIsModifyXMin = isModifyXMin;
                allIsModifyXMax = isModifyXMax;
                allIsModifyYMin = isModifyYMin;
                allIsModifyYMax = isModifyYMax;
                allModifyIndex = i;
            }
        }
        if(allMinDist == null)
        {
            break;
        }
        if(allModifyIndex != null)
        {
            recordIndexs.push(allModifyIndex);
            if(allIsModifyXMin)
            {
                copySourceRange.xMin = Math.min(copySourceRange.xMin, allMinRange.xMax);
            }
            if(allIsModifyXMax)
            {
                copySourceRange.xMax = Math.max(copySourceRange.xMax, allMinRange.xMin);
            }
            if(allIsModifyYMin)
            {
                copySourceRange.yMin = Math.min(copySourceRange.yMin, allMinRange.yMax);
            }
            if(allIsModifyYMax)
            {
                copySourceRange.yMax = Math.max(copySourceRange.yMax, allMinRange.yMin);
            }
        }
    }
}

function postCutRangeByRoom(room: TRoom, range: any, sourceRange: any, sourceCenter: Vector3, areaRatioTol: number = 0.6)
{
    let rangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(range, TBaseRoomToolUtil.instance.getCenter3dByRange2d(range));
    let xRectEdges: ZEdge[] = rangeRect.edges.filter(edge => Math.abs(edge.dv.clone().dot(xDir)) > 0.9);
    let yRectEdges: ZEdge[] = rangeRect.edges.filter(edge => Math.abs(edge.dv.clone().dot(yDir)) > 0.9);
    let xScore: number = 0;
    let yScore: number = 0;
    let xLayonRoomEdges: ZEdge[] = [];
    let yLayonRoomEdges: ZEdge[] = [];
    let cutXLayonRoomEdges: ZEdge[] = [];
    let cutYLayonRoomEdges: ZEdge[] = [];
    for(let xRectEdge of xRectEdges)
    {
        let layonRoomEdgeInfos: any[] = calRoomEdgeMaxLayonRatio(xRectEdge, room.room_shape._poly.edges);
        let sumLayonRatio: number = calSumLayonRatio(layonRoomEdgeInfos);
        if(Math.abs(1 - sumLayonRatio) < 0.01)
        {
            xScore += 1;
        }
        else
        {
            xScore -= 1;
            cutXLayonRoomEdges.push(...layonRoomEdgeInfos.map(layonRoomEdgeInfo => layonRoomEdgeInfo.layonRoomEdge));
        }
        xLayonRoomEdges.push(...layonRoomEdgeInfos.map(layonRoomEdgeInfo => layonRoomEdgeInfo.layonRoomEdge));
    }
    
    for(let yRectEdge of yRectEdges)
    {
        let layonRoomEdgeInfos: any[] = calRoomEdgeMaxLayonRatio(yRectEdge, room.room_shape._poly.edges);
        let sumLayonRatio: number = calSumLayonRatio(layonRoomEdgeInfos);
        if(Math.abs(1 - sumLayonRatio) < 0.01)
        {
            yScore += 1;
        }
        else
        {
            yScore -= 1;
            cutYLayonRoomEdges.push(...layonRoomEdgeInfos.map(layonRoomEdgeInfo => layonRoomEdgeInfo.layonRoomEdge));
        }
        yLayonRoomEdges.push(...layonRoomEdgeInfos.map(layonRoomEdgeInfo => layonRoomEdgeInfo.layonRoomEdge));
    }
    let isModifyX: boolean = Math.abs(2 - xScore) > 0.01;
    let isModifyY: boolean = Math.abs(2 - yScore) > 0.01;
    let sourceRangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(sourceRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(sourceRange));
    let candidateRangeInfos: any[] = [];
    if(isModifyX)
    {
        for(let xLayonRoomEdge of cutXLayonRoomEdges)
        {
            let isContainOtherX: boolean = isEdgeOverlayOtherEdgeByXOrY(xLayonRoomEdge, xLayonRoomEdges, false);
            if(!isContainOtherX)
            {
                continue;
            }
            let xLayonRoomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(xLayonRoomEdge);
            let tempRange: any = {xMin: xLayonRoomEdgeRange.xMin, xMax: xLayonRoomEdgeRange.xMax, yMin: range.yMin, yMax: range.yMax};
            let tempRangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(tempRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(tempRange));
            let intersectRect: ZRect = tempRangeRect.intersect_rect(sourceRangeRect);
            if(!intersectRect)
            {
                continue;
            }
            let areaRatio: number = intersectRect.area / sourceRangeRect.area;
            if(areaRatio > areaRatioTol && isRangeContainPoint(tempRange, sourceCenter))
            {
                candidateRangeInfos.push({range: tempRange, areaRatio: areaRatio});
            }
        }
    }
    if(isModifyY)
    {
        for(let yLayonRoomEdge of cutYLayonRoomEdges)
        {
            let isContainOtherY: boolean = isEdgeOverlayOtherEdgeByXOrY(yLayonRoomEdge, yLayonRoomEdges, true);
            if(!isContainOtherY)
            {
                continue;
            }
            let yLayonRoomEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(yLayonRoomEdge);
            let tempRange: any = {xMin: range.xMin, xMax: range.xMax, yMin: yLayonRoomEdgeRange.yMin, yMax: yLayonRoomEdgeRange.yMax};
            let tempRangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(tempRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(tempRange));
            let intersectRect: ZRect = tempRangeRect.intersect_rect(sourceRangeRect);
            if(!intersectRect)
            {
                continue;
            }
            let areaRatio: number = intersectRect.area / sourceRangeRect.area;
            if(areaRatio > areaRatioTol && isRangeContainPoint(tempRange, sourceCenter))
            {
                candidateRangeInfos.push({range: tempRange, areaRatio: areaRatio});
            }
        }
    }
    // 在众多的可选列表中选占比最大的
    if(candidateRangeInfos.length > 0)
    {
        let maxAreaRatio: number = null;
        let maxAreaRatioRange: any = null;
        for(let candidateRangeInfo of candidateRangeInfos)
        {
            if(maxAreaRatio == null)
            {
                maxAreaRatio = candidateRangeInfo.areaRatio;
                maxAreaRatioRange = candidateRangeInfo.range;
            }
            if(candidateRangeInfo.areaRatio > maxAreaRatio)
            {
                maxAreaRatio = candidateRangeInfo.areaRatio;
                maxAreaRatioRange = candidateRangeInfo.range;
            }
        }
        range.xMin = Math.max(range.xMin, maxAreaRatioRange.xMin);
        range.xMax = Math.min(range.xMax, maxAreaRatioRange.xMax);
        range.yMin = Math.max(range.yMin, maxAreaRatioRange.yMin);
        range.yMax = Math.min(range.yMax, maxAreaRatioRange.yMax);
    }
    else
    {
        let tempLayonRoomRectEdges: ZEdge[] = [];
        let tempLayonRoomRectEdgeInfos: Map<ZEdge, ZEdge[]> = new Map();
        for(let rectEdge of rangeRect.edges)
        {
            let layonRoomEdgeInfos: any[] = calRoomEdgeMaxLayonRatio(rectEdge, room.room_shape._poly.edges);
            if(layonRoomEdgeInfos.length == 0)
            {
                continue;
            }
            tempLayonRoomRectEdges.push(rectEdge);
            let sumLayonRatio: number = calSumLayonRatio(layonRoomEdgeInfos);
            if(Math.abs(1 - sumLayonRatio) < 0.01)
            {
                continue;
            }
            tempLayonRoomRectEdgeInfos.set(rectEdge, layonRoomEdgeInfos.map(layonRoomEdgeInfo => layonRoomEdgeInfo.layonRoomEdge));
        }

        for(let item of tempLayonRoomRectEdgeInfos.entries())
        {
            for(let tempRoomEdge of item[1])
            {
                let xDot: number = tempRoomEdge.dv.clone().dot(xDir);
                let yDot: number = tempRoomEdge.dv.clone().dot(yDir);
                let tempRoomRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(tempRoomEdge);
                if(Math.abs(xDot) > 0.9)
                {
                    let isContainOtherX: boolean = isEdgeOverlayOtherEdgeByXOrY(tempRoomEdge, xLayonRoomEdges, false);
                    if(!isContainOtherX)
                    {
                        continue;
                    }
                    // 更改X
                    let tempRange: any = {xMin: tempRoomRange.xMin, xMax: tempRoomRange.xMax, yMin: range.yMin, yMax: range.yMax};
                    let tempRangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(tempRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(tempRange));
                    let intersectRect: ZRect = tempRangeRect.intersect_rect(sourceRangeRect);
                    if(!intersectRect)
                    {
                        continue;
                    }
                    let areaRatio: number = intersectRect.area / sourceRangeRect.area;
                    if(areaRatio > areaRatioTol && isRangeContainPoint(tempRange, sourceCenter))
                    {
                        range.xMin = Math.max(range.xMin, tempRange.xMin);
                        range.xMax = Math.min(range.xMax, tempRange.xMax);
                    }
                }
                else if(Math.abs(yDot) > 0.9)
                {
                    let isContainOtherY: boolean = isEdgeOverlayOtherEdgeByXOrY(tempRoomEdge, yLayonRoomEdges, false);
                    if(!isContainOtherY)
                    {
                        continue;
                    }
                    // 更改Y
                    let tempRange: any = {xMin: range.xMin, xMax: range.xMax, yMin: tempRoomRange.yMin, yMax: tempRoomRange.yMax};
                    let tempRangeRect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(tempRange, TBaseRoomToolUtil.instance.getCenter3dByRange2d(tempRange));
                    let intersectRect: ZRect = tempRangeRect.intersect_rect(sourceRangeRect);
                    if(!intersectRect)
                    {
                        continue;
                    }
                    let areaRatio: number = (intersectRect.area || intersectRect.w * intersectRect.w) / sourceRangeRect.area;
                    if(areaRatio > areaRatioTol && isRangeContainPoint(tempRange, sourceCenter))
                    {
                        range.yMin = Math.max(range.yMin, tempRange.yMin);
                        range.yMax = Math.min(range.yMax, tempRange.yMax);
                    }
                }
            }
        }
    }
}

function calRoomEdgeMaxLayonRatio(rectEdge: ZEdge, roomEdges: ZEdge[], tol_nor: number = 5, layon_radio: number = 0.1): any[]
{
    let layonRoomEdgeInfos: any[] = [];
    for(let roomEdge of roomEdges)
    {
        let l_res: { layon_len?: number, ll?: number, rr?: number } = {};
        if(!TBaseRoomToolUtil.instance.isParallelTwoEdges(rectEdge, roomEdge) || !roomEdge.islayOn(rectEdge, tol_nor, layon_radio, l_res))
        {
            continue;
        }
        let ratio: number = l_res.layon_len / rectEdge.length;
       layonRoomEdgeInfos.push({layonRatio: ratio, layonRoomEdge: roomEdge});
    }
    return layonRoomEdgeInfos;
}

function isRangeContainPoint(range: any, point: Vector3): boolean
{
    return range.xMin <= point.x && range.xMax >= point.x && range.yMin <= point.y && range.yMax >= point.y;
}

function isEdgeOverlayOtherEdgeByXOrY(edge: ZEdge, edges: ZEdge[], isY: boolean): boolean
{
    let otherEdges: ZEdge[] = edges.filter(otherEdge => otherEdge != edge);
    let edgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(edge);
    for(let otherEdge of otherEdges)
    {
        let otherEdgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(otherEdge);
        if(isY)
        {
            if(otherEdgeRange.yMin <= edgeRange.yMax && otherEdgeRange.yMax >= edgeRange.yMin)
            {
                return true;
            }
        }
        else
        {
            if(otherEdgeRange.xMin <= edgeRange.xMax && otherEdgeRange.xMax >= edgeRange.xMin)
            {
                return true;
            }
        }
        
    }
    return false;
}

function calSumLayonRatio(layonRoomEdgeInfos: any[]): number
{
    let sumLayonRation: number = 0;
    layonRoomEdgeInfos.forEach(layonRoomEdgeInfo => 
        {
            sumLayonRation += layonRoomEdgeInfo.layonRatio;
        });
    return sumLayonRation;
}

function isInAllRects(allRects: ZRect[], len: number, depth: number, center: Vector3): boolean
{
    let min_hh: number = Math.min(len, depth);
    let max_hh: number = Math.max(len, depth);
    let isExist: boolean = false;
    for(let rect of allRects)
    {
        if(rect.rect_center.distanceTo(center) < 1 && Math.abs(rect.min_hh - min_hh) < 1 && Math.abs(rect.max_hh - max_hh) < 1)
        {
            isExist = true;
            break;
        }
    }
    return isExist;
}