

import { TRoomShape } from "@/Apps/LayoutAI/Layout/TRoomShape";
import { BooleanKeyframeTrack, Vector3 } from "three";
import { LayoutAI_App } from "../../../../../../LayoutAI_App";
import { WPolygon } from "../../../../../Layout/TFeatureShape/WPolygon";
import { TRoom } from "../../../../../Layout/TRoom";
import { compareNames } from "@layoutai/z_polygon";
import { ZEdge, ZPolygon } from "@layoutai/z_polygon";
import { ZRect } from "@layoutai/z_polygon";
import { TGroupTemplate } from "../../../TGroupTemplate/TGroupTemplate";
import { I_TinyLayoutLogicStep, I_TinyLayoutParams, layout_rect_name_dict } from "../../TinyLogicFlow/TinyLayoutLogicFlow";
import { TinyLogicFlow } from "../../TinyLogicFlow/TinyLogicFlow";
import { TSimpleRoomPartRelation } from "./TSimpleRoomPartRelation";
import { I_Window } from "@/Apps/LayoutAI/Layout/IRoomInterface";
import { TBaseRoomToolUtil } from "@/Apps/LayoutAI/Layout/TLayoutScore/CheckRules/BasicCheckRules/TBaseRoomToolUtil";

const min_toilet_length = 650;
const min_cabinet_length = 600;
const min_opendoor_length = 900;
const min_shower_area_length = 800;
const max_shower_area_distance_to_wall = 1300;
const GridLength = 800;

const xDir: Vector3 = new Vector3(1, 0, 0);
const yDir: Vector3 = new Vector3(0, 1, 0);

export interface I_TinyWashingRoomLayoutParams extends I_TinyLayoutParams {
    rule_text?: string;
    candidate_main_rects?: ZRect[];
    candidate_wardrobe_rects?: ZRect[];
    candidate_valid_rects?: ZRect[];
    inner_grid_rects?: ZRect[];
    window_rects?: ZRect[];
    door_rects?: ZRect[];
    main_rect?: ZRect;
    windows?: I_Window[];

    grid_0?: ZRect;
    grid_1?: ZRect;
    grid_2?: ZRect;

    _flow_score?: number;

    /**
     *  浴室柜矩形
     */
    washing_cabinet_rect?: ZRect;
    /**
     *  马桶矩形
     */
    toilet_rect?: ZRect;
    /**
     *  花洒矩形
     */
    shower_head_rect?: ZRect;
    /**
     *  一字形淋浴房矩形
     */
    one_shower_room_rect?: ZRect;
    /**
     *  钻石形淋浴房
     */
    diamond_shower_room_rect?: ZRect;

    /**
     *  马桶-毛巾架
     */
    towel_rack_rect?: ZRect;
}

const finetune_step = "finetune_step";
const common_step_final = "Final";

const BackLayonEdge = "BackLayonEdge";

type WashingRoomFigureCategory = "toilet" | "washing_cabinet" | "one_shower_room" | "diamond_shower_room";
interface I_FineTuneWashingRoomStep {
    category: WashingRoomFigureCategory,
    length?: number,
    depth?: number,
    distanceToWall?: number,
    sideToWall?: number,
    order?: number,
    allow_error?: boolean;
}
/**
 * <AUTHOR> Wang
 * @date 2024.12.09
 * @description
 *   Tiny: AI布局产品经理---小小同学的英文昵称
 *    --- 卫生间AI布局逻辑, 由小小同学给出, 请参考对应xmind
 */
export class TTinyWashingRoomRelation extends TSimpleRoomPartRelation {
    _logic_flow: TinyLogicFlow;

    protected ans_params_list: I_TinyWashingRoomLayoutParams[];
    constructor() {
        super();
        this.initLogicFlow();
    }

    /**
     *  初始化逻辑流程图
     */
    initLogicFlow() {
        this._logic_flow = new TinyLogicFlow();
        this._logic_flow._quiet = true;
        this._logic_flow._root.description = "小小卫浴逻辑";

        this._initCommonSteps();

        this._logic_flow._root.loop_steps = [this._initSimpleLayoutSteps()];
        this._max_attempt_num = 30;


    }
    precompute(): void {
        // super.precompute();
        this._candidate_figure_list = [];

        if (!this._room) return;

        if (!compareNames([this._room.roomname], ["卫生间"])) return;
        if (!this._room.room_shape._feature_shape) {
            this._room.updateFeatures();
        }

        let params: I_TinyWashingRoomLayoutParams = {
            _room: this._room,
            _w_poly: this._room.room_shape._feature_shape._w_poly,
            _group_templates: []
        }

        // console.time("tiny logic");
        this._logic_flow.apply(this._logic_flow._root, params);

        this._candidate_figure_list = this._candidate_figure_list.sort((a, b) => b.group_templates.length - a.group_templates.length);
        this.filterCandidates();

        // this._candidate_figure_list.sort((a,b)=>b.debug_data._flow_score - a.debug_data._flow_score);


        this._attempt_num = this._candidate_figure_list.length;

        if (!LayoutAI_App.IsDebug) {
            this._attempt_num = Math.min(this._attempt_num, 10);
            this._candidate_figure_list.length = this._attempt_num;
        }
        this._candidate_figure_list.forEach((candidate, index) => candidate.debug_data.scheme_name += index);

        // console.timeEnd("tiny logic");


    }
    protected _initCommonSteps() {
        let computeAreaScore = (params: I_TinyWashingRoomLayoutParams, groupTemplates: TGroupTemplate[]) => {
            let washing_cabinet_length = 0;
            params.washing_cabinet_rect && (washing_cabinet_length = params.washing_cabinet_rect.w);

            let toilet_length = 0;
            params.toilet_rect && (toilet_length = params.toilet_rect.w);

            let shower_head_length = 0;
            params.shower_head_rect && (shower_head_length = params.shower_head_rect.w);

            let one_shower_room_length = 0;
            params.one_shower_room_rect && (one_shower_room_length = params.one_shower_room_rect.w);

            let diamond_shower_room_length = 0;
            params.diamond_shower_room_rect && (diamond_shower_room_length = params.diamond_shower_room_rect.w);


            if (washing_cabinet_length < 10) {
                one_shower_room_length *= 0.5;
            }
            one_shower_room_length = Math.min(one_shower_room_length, 1600);

            return washing_cabinet_length + shower_head_length + toilet_length + (one_shower_room_length + diamond_shower_room_length);

        }


        let final_step: I_TinyLayoutLogicStep = {
            description: "合成布局",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                let t_group_templates: TGroupTemplate[] = [];
                let name_dict = layout_rect_name_dict;
                for (let key in name_dict) {
                    if (params[key]) {
                        let group_space_category = (name_dict as any)[key];


                        if (params[key] instanceof Array) {
                            let target_rects: ZRect[] = params[key];
                            target_rects.forEach((rect) => {
                                let group_tempalte = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(group_space_category, "卫生间", rect, {needs_set_group_code:false,consider_depth:false});
                                if (group_tempalte && group_tempalte.current_s_group) {
                                    t_group_templates.push(group_tempalte);
                                }
                            })
                        }
                        else {
                            let group_tempalte = TGroupTemplate.makeGroupTemplateByGroupSpaceCategory(group_space_category, "卫生间", params[key], {needs_set_group_code:false,consider_depth:false});
                            if (group_tempalte && group_tempalte.current_s_group) {
                                t_group_templates.push(group_tempalte);
                            }
                        }

                    }
                }




                if (t_group_templates.length > 0) {
                    this._candidate_figure_list.push({ group_templates: t_group_templates, debug_data: { _flow_score: computeAreaScore(params, t_group_templates), scheme_name: "逻辑-" + (params.rule_text || "小小布局") } });
                }

                return null;
            }
        }


        let _finetune_step: I_TinyLayoutLogicStep = {
            description: "微调布局",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                let toilet_rect = params.toilet_rect;
                let one_shower_room_rect = params.one_shower_room_rect;
                let diamond_shower_room_rect = params.diamond_shower_room_rect;
                let washing_cabinet_rect = params.washing_cabinet_rect;               

                if (!toilet_rect || !washing_cabinet_rect) return null;

                let fine_tune_steps: I_FineTuneWashingRoomStep[] = [
                    {
                        category: "washing_cabinet",
                        length: 600,
                        allow_error: true
                    },
                    {
                        category: "toilet",
                        length: 700,
                        allow_error: true

                    },
                    {
                        category: "toilet",
                        sideToWall: 0,

                    },
                    {
                        category: "one_shower_room",
                        distanceToWall: 800
                    },
                    {
                        category: "diamond_shower_room",
                        length: 900,
                        depth: 900
                    },
                    {
                        category: "one_shower_room",
                        distanceToWall: 900
                    },
                    {
                        category: "toilet",
                        length: 750,
                    },
                    {
                        category: "toilet",
                        length: 800
                    },
                    {
                        category: "washing_cabinet",
                        length: 650
                    },
                    {
                        category: "washing_cabinet",
                        length: 700
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1000,
                        depth: 800
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1000,
                        depth: 900,
                        order: 11
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1000,
                        depth: 1000,
                        order: 11
                    },
                    {
                        category: "one_shower_room",
                        distanceToWall: 1000
                    },
                    {
                        category: "washing_cabinet",
                        length: 750,
                        order: 12
                    },
                    {
                        category: "toilet",
                        length: 850,
                        order: 13
                    },
                    {
                        category: "washing_cabinet",
                        length: 800,
                        order: 14
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1100,
                        depth: 1000
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1000,
                        depth: 1100
                    },
                    {
                        category: "diamond_shower_room",
                        length: 1100,
                        depth: 1100,
                    },
                    {
                        category: "one_shower_room",
                        distanceToWall: 1100
                    },
                    {
                        category: "toilet",
                        length: 900,
                        order: 17
                    }, {
                        category: "washing_cabinet",
                        length: 850,
                        order: 18
                    }, {
                        category: "washing_cabinet",
                        length: 900,
                        order: 19
                    }, {
                        category: "diamond_shower_room",
                        length: 1200,
                        depth: 1200
                    }, {
                        category: "one_shower_room",
                        distanceToWall: 1200
                    }, {
                        category: "washing_cabinet",
                        length: 950,
                        order: 21
                    }, {
                        category: "washing_cabinet",
                        length: 1000,
                        order: 22
                    },
                    {
                        category: "washing_cabinet",
                        length: 1100,
                        order: 23
                    },
                    {
                        category: "washing_cabinet",
                        length: 1200
                    }
                ]

                let w_poly = params._w_poly;
                let target_names = ["washing_cabinet_rect", "toilet_rect", "one_shower_room_rect", "diamond_shower_room_rect"];
                let wall_rects = params._w_poly.edges.map((edge) => {
                    let rect = new ZRect(edge.length, 60);
                    rect.nor = edge.nor;
                    rect.back_center = edge.unprojectEdge2d({ x: edge.length / 2, y: 30 });
                    rect.updateRect();
                    return rect;
                })
                let door_rects = params.door_rects.map((rect) => {
                    if (rect.w > 1400) return null
                    let t_rect = rect.clone();
                    let r_center = t_rect.rect_center;
                    t_rect._w = Math.min(min_opendoor_length, rect.w);
                    t_rect._h += Math.min(min_opendoor_length, rect.w) * 2;
                    t_rect.rect_center = r_center;
                    return t_rect;
                }).filter((rect) => rect);
                const checkIsValid = (params: I_TinyWashingRoomLayoutParams, target_rect: ZRect) => {
                    let target_rects: ZRect[] = [];
                    target_names.forEach((name) => {
                        if (params[name]) {
                            let t_rect: ZRect = params[name];
                            if (t_rect === target_rect) return;
                            t_rect = t_rect.clone();
                            if (name === "washing_cabinet_rect") {
                                t_rect._h += 450;
                                t_rect.updateRect();
                            }
                            t_rect.reOrderByOrientation(true);
                            target_rects.push(t_rect);

                        }
                    });
                    let collision_rects = [...wall_rects, ...door_rects, ...target_rects];
                    let checkCollision = false;
                    for (let rect0 of collision_rects) {
                        if (checkCollision) break;
                        let rect1 = target_rect;
                        if (rect0 === rect1) continue;

                        let int_rect = rect0.intersect_rect(rect1);

                        if (int_rect && int_rect.min_hh > 15) {
                            checkCollision = true;
                        }
                        if (checkCollision) break;

                    }
                    if (checkCollision) return false;
                    return true;
                }
                let one_shower_room_front_edge: ZEdge = null;
                if (params.one_shower_room_rect) {
                    let int_p = params._w_poly.getRayIntersection(params.one_shower_room_rect.rect_center, params.one_shower_room_rect.nor);
                    one_shower_room_front_edge = int_p.edge;
                }
                const applyStep = (params: I_TinyWashingRoomLayoutParams, step: I_FineTuneWashingRoomStep) => {
                    let rect: ZRect = params[step.category + "_rect"];
                    if (!rect) return true;

                    let t_rect = rect.clone();

                    let side_edges_list = [t_rect.leftEdge, t_rect.rightEdge];

                    let  side_edge = w_poly.edges.find(edge => {
                        let s_w_edge = side_edges_list.find(rect_edge => {
                            if (edge.checkSameNormal(rect_edge.nor)) {
                                return edge.islayOn(rect_edge, 200, 0.2);
                            }
                            return false;
                        })
                        return s_w_edge ? true : false;
                    });

                    let t_length = step.length || rect.w;
                    let t_depth = step.depth || rect.h;

                    let r_center = t_rect.rect_center;
                    let old_w = rect.w;
                    t_rect._w = t_length;
                    t_rect._h = t_depth;

                    if (side_edge) {
                        let pp = side_edge.projectEdge2d(t_rect.rect_center);
                        t_rect.rect_center = side_edge.unprojectEdge2d({ x: pp.x, y: -t_length / 2 });
                    }
                    else {
                        t_rect.updateRect();
                    }

                    if (step.distanceToWall) {
                        if (step.category === "one_shower_room") {
                            if (one_shower_room_front_edge) {
                                let pp = one_shower_room_front_edge.projectEdge2d(t_rect.rect_center);
                                pp.y = -step.distanceToWall;
                                t_rect.rect_center = one_shower_room_front_edge.unprojectEdge2d(pp);
                            }
                        }
                    }
                    if (step.sideToWall !== undefined) {
                        let s_w_edge = w_poly.edges.find(edge => {
                            let res = side_edges_list.find((rect_edge) => {
                                if (edge.checkSameNormal(rect_edge.nor)) {
                                    return edge.islayOn(rect_edge, GridLength * 0.9, 0.2) && isLayonValidect(rect_edge, rect, params);
                                }
                            });
                            if (res) return true;

                        })

                        if (s_w_edge) {
                            let pp = s_w_edge.projectEdge2d(t_rect.rect_center);
                            let r_center = s_w_edge.unprojectEdge2d({ x: pp.x, y: -t_rect._w / 2 - step.sideToWall });
                            t_rect.rect_center = r_center;
                        }
                        else {
                            return true;
                        }
                    }

                    params[step.category + "_rect"] = t_rect;

                    let is_valid = step.allow_error || checkIsValid(params, t_rect);
                    if (is_valid) {
                        return true;
                    }
                    else {
                        params[step.category + "_rect"] = rect;
                        return false;
                    }


                }
                for (let sub_step of fine_tune_steps) {
                    applyStep(params, sub_step);
                    //    if(! applyStep(params,sub_step)){
                    //         break;
                    //    }

                }

                return {
                    results: [params]
                };
            }
        }

        let update_shower_head_step: I_TinyLayoutLogicStep = {
            description: "最后调整",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                if (params.shower_head_rect) {
                    let t_rect = params.shower_head_rect.clone();
                    t_rect._w = 400;
                    t_rect._h = 600;
                    t_rect.updateRect();

                    // TODO 这里插入补丁， 当花洒layon窗时，则将其旋转至
                    checkRectBackOnWindow(params._room, t_rect);
                    params.shower_head_rect = t_rect;
                }
                if (params.washing_cabinet_rect) {
                    if (params.washing_cabinet_rect.w < 600) {
                        params.washing_cabinet_rect = null;
                    }
                }
                if (params.toilet_rect) {
                    if (params.toilet_rect.w < 600) {
                        params.toilet_rect = null;
                    }

                    if (params.toilet_rect) {
                        params.towel_rack_rect = params.toilet_rect.clone();
                        params.towel_rack_rect._w = 600;
                        params.towel_rack_rect._h = 300;
                        params.towel_rack_rect.updateRect();
                    }
                }

                return null;
            }
        }
        this._logic_flow._steps_dict[common_step_final] = final_step;
        this._logic_flow._steps_dict[finetune_step] = _finetune_step;
        _finetune_step.loop_steps = [update_shower_head_step];
        update_shower_head_step.loop_steps = [final_step];
    }
    _initSimpleLayoutSteps() {
        const SrcGrid = "SrcGrid";
        const isAInfrontB = (A: ZRect, B: ZRect, distance: number) => {
            let pp = B.project(A.rect_center);
            if (Math.abs(pp.x) > B.w / 2) return false;
            if (pp.y < distance + B.h / 2) return true;
            return false;

        }
        const simpleCheckRule = (params: I_TinyWashingRoomLayoutParams) => {

            if (params.washing_cabinet_rect) // 浴室柜相关规则
            {
                if (params.toilet_rect) {
                    let washing_cabinet_rect = params.washing_cabinet_rect;
                    let toilet_rect = params.toilet_rect;
                    if (isAInfrontB(toilet_rect, washing_cabinet_rect, GridLength)) // 浴室柜前面一格有马桶
                    {
                        return false;
                    }

                }
            }
            return true;
        }
        const makeRectOfGrid = (grid: ZRect, dv: Vector3 = null) => {
            let rect = grid.clone();
            let props = WPolygon._getGridRectProps(grid);
            let wall_edges = props.align_wall_edges || [];

            if (dv) {
                wall_edges = [...wall_edges];
                wall_edges.sort((a, b) => {
                    return Math.abs(a.nor.dot(dv)) - Math.abs(b.nor.dot(dv));
                });
            }

            let r_center = rect.rect_center;
            if (wall_edges[0]) {
                rect = ZRect.fromPoints(rect.positions, wall_edges[0].nor.clone().negate());
            }
            if (dv) {

                rect.u_dv = dv;
            }
            rect.rect_center = r_center;
            rect._attached_elements[SrcGrid] = grid;
            return rect;
        }
        const makeRectListOfGrid = (grid: ZRect, windows: I_Window[], roomPoly: ZPolygon) => {
            let props = WPolygon._getGridRectProps(grid);
            let nor_list: Vector3[] = [];
            let ans_rects: ZRect[] = [];
            props.align_wall_edges.forEach((edge) => {
                if(isLayonWindows(edge, windows) || edge.length < 100 || !isEdgeInGrid(edge, grid))
                {
                    return;
                }

                let target_nor = nor_list.find((nor) => edge.nor.dot(nor) > 0.9);
                if (target_nor) return;

                nor_list.push(edge.nor);
                let rectNor: Vector3 = edge.nor.clone().negate();
                let rectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(grid)
                let xDot: number = edge.nor.dot(xDir);
                let yDot: number = edge.nor.dot(yDir);
                if(xDot > 0.9)
                {
                    rectRange.xMax = Math.min(rectRange.xMax, edge.center.x);
                }
                else if(xDot < -0.9)
                {
                    rectRange.xMin = Math.max(rectRange.xMin, edge.center.x);
                }

                if(yDot > 0.9)
                {
                    rectRange.yMax = Math.min(rectRange.yMax, edge.center.y);
                }
                else if(yDot < -0.9)
                {
                    rectRange.yMin = Math.max(rectRange.yMin, edge.center.y);
                }
                let rectCenter: Vector3 = TBaseRoomToolUtil.instance.getCenter3dByRange2d(rectRange);
                let rect: ZRect = TBaseRoomToolUtil.instance.getRectByRange2d(rectRange, rectCenter);
                let intersectPolys: ZPolygon[] = rect.intersect(roomPoly);
                if(intersectPolys && intersectPolys.length > 0)
                {
                    let maxRect: ZRect = null;
                    for(let intersectPoly of intersectPolys)
                    {
                        let allInnerRects: ZRect[] = TBaseRoomToolUtil.instance.getAllInnerRectByPolygon(intersectPoly);
                        allInnerRects.sort((rect1, rect2) => rect2.area - rect1.area);
                        for(let innerRect of allInnerRects)
                        {
                            if(TBaseRoomToolUtil.instance.edgeOnPolygon(edge, innerRect))
                            {
                                if(maxRect == null || maxRect.area < innerRect.area)
                                {
                                    maxRect = innerRect;
                                }
                            }
                        }
                        
                    }  
                    if(maxRect)
                    {
                        rectCenter = maxRect.rect_center;
                        rect = maxRect;
                    }
                }
                    
                if(Math.abs(rect.nor.dot(rectNor)) < 0.9)
                {
                    rect.swapWidthAndHeight();
                }
                rect.nor = rectNor;
                rect.rect_center = rectCenter;
                rect.updateRect();
                rect._attached_elements[SrcGrid] = grid;
                ans_rects.push(rect);
            });

            ans_rects.forEach((rect) => {
                nor_list.forEach((nor) => {
                    if (rect.checkSameDirection(nor, true, 0.3)) {
                        rect.u_dv = nor.clone().negate();
                        rect.updateRect();
                    }
                })
            })
            return ans_rects;
        }
        let first_step: I_TinyLayoutLogicStep = {
            description: "格子分区-以及花洒格子",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                let w_poly = params._w_poly;
                let room = params._room;
                let windows: I_Window[] = room.windows.filter(win => win.type === "Window");
                params.windows = windows;

                let door_rects = room.windows.map((win) => {
                    if (win.type === "Door") {
                        return win.rect;
                    }
                    else {
                        return null;
                    }
                }).filter((rect) => rect);

                let inner_grid_rects = WPolygon._computeInnerGridRects(w_poly, door_rects);
                params.inner_grid_rects = inner_grid_rects;
                params.door_rects = door_rects;


                let results: I_TinyWashingRoomLayoutParams[] = [];


                let candidate_grids = inner_grid_rects.filter((grid) => {
                    let props = WPolygon._getGridRectProps(grid);
                    if (props.wallOverlapLength > grid.max_hh * 1.5) {
                        return true;
                    }
                    return false;
                });

                // TODO fxy: 剔除掉一些花洒区域
                candidate_grids = filterShowerHeadCandidateGrids(candidate_grids, inner_grid_rects, door_rects);

                if (candidate_grids.length > 0) {
                    candidate_grids.length = Math.min(2, candidate_grids.length);
                    candidate_grids.forEach((grid) => {
                        let result: I_TinyWashingRoomLayoutParams = {
                            grid_0: grid
                        }
                        results.push(result);
                    })

                }
                else {
                    let result: I_TinyWashingRoomLayoutParams = {
                        grid_0: inner_grid_rects[0]
                    }
                    results.push(result);
                }

                let result_with_targets: I_TinyWashingRoomLayoutParams[] = [];
                results.forEach((result) => {
                    let grid0 = result.grid_0;
                    if (grid0) {
                        let valid_rects = makeRectListOfGrid(grid0, params.windows, params._room.room_shape._poly);
                        valid_rects.forEach((rect) => {
                            let t_result: I_TinyWashingRoomLayoutParams = {
                                grid_0: grid0,
                                shower_head_rect: rect
                            };
                            result_with_targets.push(t_result);
                        })
                    }


                })

                return {
                    results: result_with_targets
                }
            }
        }

        let second_step: I_TinyLayoutLogicStep = {
            description: "格子2-马桶区",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                if (!params.inner_grid_rects) return {
                    results: []
                };

                let grid_0 = params.grid_0;

                let results: I_TinyWashingRoomLayoutParams[] = [];

                let inner_grids = params.inner_grid_rects;

                inner_grids.forEach((grid) => {
                    if (grid === grid_0) return;
                    let grid_props = WPolygon._getGridRectProps(grid);
                    if (!grid_props.isAlignWall) return;

                    let is_valid = true;

                    params.door_rects.forEach((door_rect) => {
                        let pp = door_rect.project(grid.rect_center);
                        if (Math.abs(pp.x) < 600) is_valid = false;
                    })
                    if (!is_valid) return;

                    let t_rect_list = makeRectListOfGrid(grid, params.windows, params._room.room_shape._poly);
                    t_rect_list.forEach((rect) => {
                        if (rect.w < min_toilet_length - 10) return;
                        let result: I_TinyWashingRoomLayoutParams = {
                            grid_1: grid,
                            toilet_rect: rect
                        }
                        results.push(result);
                    })
                })

                if (results.length == 0) {
                    return null;
                }
                return {
                    results: results
                };
            }
        }

        let third_step: I_TinyLayoutLogicStep = {
            description: "格子3-浴室柜",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                if (!params.inner_grid_rects) return {
                    results: []
                };
                let visited_grids = [params.grid_0, params.grid_1];
                let grid0 = params.grid_0;
                let grid1 = params.grid_1;
                if (!grid0 || !grid1) return null;

                let nor = params.toilet_rect.nor;
                let results: I_TinyWashingRoomLayoutParams[] = [];

                // 浴室柜处于马桶正前方的格子都标为已访问
                let valid_grids = [params.grid_1];
                valid_grids.forEach((grid) => {
                    let grid_props = WPolygon._getGridRectProps(grid);
                    grid_props.neighbor_grids.forEach((n_grid) => {
                        if (visited_grids.includes(n_grid)) return;
                        let n_props = WPolygon._getGridRectProps(n_grid);
                        if (!n_props.isAlignWall) return;
                        let n_dv = n_grid.rect_center.clone().sub(grid.rect_center).normalize();
                        if (Math.abs(n_dv.dot(nor)) < 0.8) return;
                        visited_grids.push(n_grid);
                    });
                })

                valid_grids.push(params.grid_0);

                // 将卫生间内部格子反转排序，寻找浴室柜
                let inner_grid_rects = [...params.inner_grid_rects];
                inner_grid_rects = inner_grid_rects.reverse();
                inner_grid_rects.forEach((n_grid) => {
                    if (visited_grids.includes(n_grid)) return;
                    let n_props = WPolygon._getGridRectProps(n_grid);
                    if (!n_props.isAlignWall) return;
                    let t_rect_list = makeRectListOfGrid(n_grid, params.windows, params._room.room_shape._poly);
                    t_rect_list.forEach((rect) => {
                        let result: I_TinyWashingRoomLayoutParams = {
                            grid_2: n_grid,
                            washing_cabinet_rect: rect
                        }
                        results.push(result);
                    })

                })

                if (results.length == 0) {
                    return null;
                }
                return {
                    results: results
                };

                return null;
            }
        }


        let layout_step: I_TinyLayoutLogicStep = {
            description: "初次过筛一批数据",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                if (!simpleCheckRule(params)) {
                    return {
                        results: []
                    }
                }
                else {
                    return null;
                }
            }
        }


        let expand_step: I_TinyLayoutLogicStep = {
            description: "扩充浴室柜区",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                let visited_grids: ZRect[] = [params.grid_0];

                // 马桶 和 浴室柜 前面一格不能放东西
                let add_visited_grid_method0 = (rect: ZRect, grid: ZRect) => {
                    if (!rect || !grid) return;
                    visited_grids.push(grid);

                    let grid_props = WPolygon._getGridRectProps(grid);
                    grid_props.neighbor_grids.forEach((n_grid) => {
                        let dv = n_grid.rect_center.clone().sub(grid.rect_center);
                        if (dv.dot(rect.nor) < 0.9) return;
                        if (!visited_grids.includes(n_grid)) visited_grids.push(n_grid);
                    })
                }
                if (params.shower_head_rect) {
                    let rect = params.shower_head_rect;
                    let grid = params.grid_0 as ZRect;
                    add_visited_grid_method0(rect, grid);

                }
                if (params.toilet_rect) {
                    let rect = params.toilet_rect;
                    let grid = params.grid_1 as ZRect;
                    add_visited_grid_method0(rect, grid);

                }
                if (params.washing_cabinet_rect) {
                    let rect = params.washing_cabinet_rect;
                    let grid = params.grid_2 as ZRect;
                    add_visited_grid_method0(rect, grid);
                }

                // 左右延伸 矩形
                const expand_cabinet_rects = (start_grid: ZRect, nor: Vector3, method: number) => {
                    let queue: ZRect[] = [start_grid];

                    for (let qi = 0; qi < queue.length; qi++) {
                        let q_grid = queue[qi];
                        let q_grid_props = WPolygon._getGridRectProps(q_grid);
                        q_grid_props.neighbor_grids.forEach((n_grid) => {
                            if (visited_grids.includes(n_grid)) return;
                            if (queue.includes(n_grid)) return;
                            let dv = n_grid.rect_center.clone().sub(start_grid.rect_center).normalize();
                            if (Math.abs(dv.dot(nor)) > 0.1) return; // 只能水平延申

                            let props = WPolygon._getGridRectProps(n_grid);
                            queue.push(n_grid);
                        })
                    }
                    return queue;
                }


                if (params.grid_2 && params.washing_cabinet_rect) {
                    let results: I_TinyWashingRoomLayoutParams[] = [];

                    // TODO fxy: 这里再看看计算出来的柜体数据
                    if(params.grid_2._attached_elements.GridPosId.x == 1 && params.grid_2._attached_elements.GridPosId.y == 2)
                    {
                        TBaseRoomToolUtil.instance.logRoomAndPolys(params._room.room_shape._poly, [params.washing_cabinet_rect], "绘制浴室柜1");
                    }

                    let cabinet_queues = expand_cabinet_rects(params.grid_2, params.washing_cabinet_rect.nor, 0);

                    let positions: Vector3[] = [];
                    if(cabinet_queues.length > 1)
                    {
                        cabinet_queues.forEach((rect) => positions.push(...rect.positions));
                    }
                    else
                    {
                        positions = params.washing_cabinet_rect.positions;
                    }

                    let rect = ZRect.fromPoints(positions, params.washing_cabinet_rect.nor);
                    if(params.grid_2._attached_elements.GridPosId.x == 1 && params.grid_2._attached_elements.GridPosId.y == 2)
                    {
                        TBaseRoomToolUtil.instance.logRoomAndPolys(params._room.room_shape._poly, [rect], "绘制浴室柜2");
                    }
                    rect._h = 600;
                    rect.updateRect();
                    
                    let sub_rects = [params.shower_head_rect, params.toilet_rect].filter((rect) => rect).map((rect) => {
                        let t_rect = rect.clone();
                        t_rect.reOrderByOrientation(true);
                        let r_center = t_rect.rect_center;
                        t_rect._h += 20;
                        t_rect.rect_center = r_center;
                        return t_rect;
                    });
                    params._w_poly.edges.forEach((edge) => {
                        let win = WPolygon.getWindowOnEdge(edge);
                        if (win && win.type === "Door") {
                            let t_door_rect = new ZRect(edge.length, Math.min(edge.length, 900));
                            t_door_rect.back_center = edge.center;
                            t_door_rect.nor = edge.nor.clone().negate();
                            t_door_rect.updateRect();
                            sub_rects.push(t_door_rect);
                        }
                    });
                    if (sub_rects.length > 0) {
                        let res_polys = rect.substract_polygons(sub_rects);
                        if (res_polys[0]) {
                            rect.copy(ZRect.fromPoints(res_polys[0].positions, rect.nor));
                        }
                    }

                    if (rect.w < 600)
                    {
                        rect = null;
                    }

                    let result: I_TinyWashingRoomLayoutParams = {
                        washing_cabinet_rect: rect
                    }


                    results.push(result);



                    return {
                        results: results
                    }
                }

                return null;
            }
        }
        let shower_room_step: I_TinyLayoutLogicStep = {
            description: "添加淋浴房",
            step: (params: I_TinyWashingRoomLayoutParams) => {

                let results: I_TinyWashingRoomLayoutParams[] = [];

                for (let method = 0; method < 2; method++) {
                    let result: I_TinyWashingRoomLayoutParams = {}
                    if (method == 0) //  钻石型淋浴房
                    {
                        if (params.shower_head_rect.min_hh >= 750) {
                            let unwanted_neighbor_rects = [params.washing_cabinet_rect, params.toilet_rect].filter((rect) => rect);

                            let is_valid = true;
                            unwanted_neighbor_rects.forEach((u_rect) => {
                                let shower_head_rect = params.shower_head_rect;
                                let pp = u_rect.project(shower_head_rect.rect_center);

                                if (Math.abs(pp.x) <= u_rect.w / 2) {
                                    if (pp.y >= 0 && pp.y < u_rect._h / 2 + shower_head_rect.min_hh / 2 + 600) {
                                        is_valid = false;
                                    }

                                }
                            })
                            if (!is_valid) continue;

                            // TODO fxy: 对钻石淋浴房再加一个选项, 需要前方有个空白格子方便打开
                            if(!isCanPutDiamondShowerRoom(params.shower_head_rect, [params.washing_cabinet_rect, params.toilet_rect], params.inner_grid_rects))
                            {
                                continue;
                            }

                            result.diamond_shower_room_rect = params.shower_head_rect.clone();
                            result.diamond_shower_room_rect.u_dv = params.shower_head_rect.dv.clone().negate();
                            result.diamond_shower_room_rect._h = result.diamond_shower_room_rect.w;
                            result.diamond_shower_room_rect.updateRect();

                            results.push(result);
                        }


                    }
                    else {
                        let shower_head_rect = params.shower_head_rect;
                        if(shower_head_rect.length < 750)
                        {
                            continue;
                        }
                        let right_edge = params.shower_head_rect.rightEdge;
                        let w_poly = params._w_poly;

                        let not_wanted_infront_rects = [params.toilet_rect, params.washing_cabinet_rect].filter((rect) => rect);
                        let is_valid = true;
                        not_wanted_infront_rects.forEach((t_rect) => {
                            let shower_head_rect = params.shower_head_rect;
                            let pp = shower_head_rect.frontEdge.projectEdge2d(t_rect.rect_center);

                            if (pp.x >= 0 && pp.x <= shower_head_rect.frontEdge.length) {
                                is_valid = false;;
                            }
                        });
                        if (is_valid) {
                            params.door_rects.forEach((door_rect) => {
                                let layon_edge = door_rect.backEdge.computeLayOnEdge(shower_head_rect.frontEdge, 1e10, 0.01);
                                if (layon_edge && layon_edge.length > 100) {
                                    is_valid = false;
                                }
                            })
                        }
                        if (!is_valid) {
                            continue;
                        }
                        let res = w_poly.getRayIntersection(right_edge.center, shower_head_rect.nor);
                        if (res.point) {
                            let pos0 = right_edge.v0.pos.clone();
                            let pos1 = res.point;

                            let length = pos1.clone().sub(pos0).length();

                            if (length < 1100) continue;
                            let t_rect = new ZRect(length, 50);
                            t_rect.nor = right_edge.nor.clone().negate();
                            t_rect.back_center = (pos0.add(pos1)).multiplyScalar(0.5);
                            t_rect.u_dv = shower_head_rect.nor;
                            t_rect.updateRect();

                            // 检查是否贴了门
                            let ex_t_rect = t_rect.clone();
                            ex_t_rect._h = shower_head_rect.w + 120;
                            ex_t_rect.updateRect();
                            let overlap_length = 0;
                            params.door_rects.forEach((door_rect) => {
                                let res = ex_t_rect.comparePolyDistance(door_rect, 50);
                                if (res.overlap_length) {
                                    overlap_length += res.overlap_length;
                                }
                            });

                            if (overlap_length > 450) {
                                continue;
                            }
                            // TODO fxy: 判断一字型淋浴房后面是否存在浴室柜或者马桶之类的
                            if(isOneShowerRoomInnerExistWashCabinerOrToilet(t_rect, [params.toilet_rect, params.washing_cabinet_rect]))
                            {
                                continue;
                            }

                            result.one_shower_room_rect = t_rect;

                            // 裁剪浴室柜

                            if (params.washing_cabinet_rect) {
                                let t1_rect = t_rect.clone();
                                t1_rect._h = 1e10;
                                t1_rect._w = 1e10;
                                t1_rect.updateRect();

                                let res_polys = params.washing_cabinet_rect.clone().substract(t1_rect);
                                if (res_polys[0]) {
                                    let c_rect = ZRect.fromPoints(res_polys[0].positions, params.washing_cabinet_rect.nor);
                                    result.washing_cabinet_rect = c_rect;
                                }
                            }

                            results.push(result);

                        }


                    }
                }

                if (results.length == 0) {
                    return null;
                }
                return {
                    results: results
                };
            }
        }

        let scope = this;
        let saved_params: I_TinyLayoutLogicStep = {
            description: "保存结果",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                let keys = ["washing_cabinet_rect", "toilet_rect", "shower_head_rect", "one_shower_room_rect", "diamond_shower_room_rect", "towel_rack_rect"];
                let t_params: I_TinyWashingRoomLayoutParams = { ...params };
                for (let key of keys) {
                    if (params[key]) {
                        t_params[key] = params[key].clone();
                    }
                }
                scope.ans_params_list.push(t_params);
                return null;
            }
        }
        let computeAreaScore = (params: I_TinyWashingRoomLayoutParams) => {
            let washing_cabinet_length = 0;
            params.washing_cabinet_rect && (washing_cabinet_length = params.washing_cabinet_rect.w);

            let toilet_length = 0;
            params.toilet_rect && (toilet_length = params.toilet_rect.w);

            let shower_head_length = 0;
            params.shower_head_rect && (shower_head_length = params.shower_head_rect.w);

            let one_shower_room_length = 0;
            params.one_shower_room_rect && (one_shower_room_length = params.one_shower_room_rect.w);

            let diamond_shower_room_length = 0;
            params.diamond_shower_room_rect && (diamond_shower_room_length = params.diamond_shower_room_rect.w);


            if (washing_cabinet_length < 10) {
                one_shower_room_length *= 0.5;
            }
            one_shower_room_length = Math.min(one_shower_room_length, 1600);
            return washing_cabinet_length + shower_head_length + toilet_length + (one_shower_room_length + diamond_shower_room_length);

        }

        let sort_params_step: I_TinyLayoutLogicStep = {
            description: "排序",
            step: (params: I_TinyWashingRoomLayoutParams) => {
                for (let params of scope.ans_params_list) {
                    params._flow_score = computeAreaScore(params);
                }
                scope.ans_params_list.sort((a, b) => b._flow_score - a._flow_score);
                scope.ans_params_list.length = Math.min(scope.ans_params_list.length, 25);
                return {
                    results: scope.ans_params_list
                }
            }
        }


        let steps_queue0 = [first_step, second_step, third_step, layout_step,
            expand_step,
            shower_room_step,
            saved_params];

        sort_params_step.loop_steps = [this._logic_flow._steps_dict[finetune_step]];

        for (let i = 0; i < steps_queue0.length - 1; i++) {
            steps_queue0[i].loop_steps = [steps_queue0[i + 1]];
        }

        let root_step: I_TinyLayoutLogicStep = {
            description: "基础流程",
            step: (params: I_TinyLayoutParams) => {
                this.ans_params_list = [];
                return null;
            },
            loop_steps: [first_step, sort_params_step]
        }
        return root_step;
    }

    /**
     * 微调布局
     * @param group_templates  
     */
    _finetuneGroupTemplates(group_templates: TGroupTemplate[], w_poly: ZPolygon, room: TRoom) {
        let door_rects = room.windows.map((win) => {
            if (win.type === "Door") {
                return win.rect;
            }
            else {
                return null;
            }
        }).filter((rect) => rect);

        let params: I_TinyWashingRoomLayoutParams = {};

        for (let key in layout_rect_name_dict) {
            let group_space_category = (layout_rect_name_dict as any)[key];

            let group_template = group_templates.find((gt) => gt.group_space_category === group_space_category);

            if (group_template) {
                group_template._target_rect._attached_elements[TGroupTemplate.EntityName] = group_template;
                params[key] = group_template._target_rect;
            }
        }


        const enlarge_one_shower_room = () => {
            if (!params.one_shower_room_rect) return true;

            let t_params: I_TinyWashingRoomLayoutParams = {};
            for (let key in params) {
                t_params[key] = params[key].clone();
            }
            let one_shower_room_rect = t_params.one_shower_room_rect;

            let res = w_poly.getRayIntersection(one_shower_room_rect.back_center, one_shower_room_rect.nor);
            if (!res || !res.edge) return false;
            let dist_to_wall = res.point.distanceTo(one_shower_room_rect.back_center);

            const ShowerDistLevel = [900, 1000, 1100, 1200, 1300];

            let target_val = -1;
            for (let val of ShowerDistLevel) {
                if (val > dist_to_wall) {
                    target_val = val;
                    break;
                }
            }
            if (target_val < 0) return false;

            let d_offset = target_val - dist_to_wall;

            one_shower_room_rect.back_center = one_shower_room_rect.backEdge.unprojectEdge2d({ x: one_shower_room_rect.w / 2, y: d_offset });
            one_shower_room_rect.updateRect();


            let shower_room_area_rect = one_shower_room_rect.clone();
            shower_room_area_rect._w += 120;
            shower_room_area_rect._h = target_val + 240; // 加上一些墙厚
            shower_room_area_rect.updateRect();

            let toilet_rect = t_params.toilet_rect;
            if (toilet_rect) {
                let sub_rects = [shower_room_area_rect];
                if (t_params.washing_cabinet_rect) {
                    let t_w_rect = t_params.washing_cabinet_rect.clone();
                    t_w_rect._h += 200;
                    sub_rects.push(t_w_rect);
                }

                let ans_polys = toilet_rect.substract_polygons(sub_rects);
                if (ans_polys[0]) {
                    TRoomShape.optimizePoly(ans_polys[0]);
                    let t_rect = ZRect.fromPoints(ans_polys[0].positions, toilet_rect.nor);

                    if (t_rect.w < min_toilet_length) {
                        toilet_rect.rect_center = toilet_rect.rect_center.add(one_shower_room_rect.nor.clone().negate().multiplyScalar(d_offset));
                        let t_polys = toilet_rect.intersect_polygons([w_poly]);
                        if (t_polys[0]) {
                            toilet_rect.copy(ZRect.fromPoints(t_polys[0].positions, toilet_rect.nor));
                        }
                        if (toilet_rect.w < min_toilet_length) {
                            return false;
                        }
                    }
                    else {
                        toilet_rect.copy(t_rect);
                    }
                }

                if (toilet_rect) {
                    if (t_params.towel_rack_rect) {
                        t_params.towel_rack_rect.back_center = toilet_rect.back_center;
                        t_params.towel_rack_rect._w = Math.min(params.towel_rack_rect._w, toilet_rect.w);
                        t_params.towel_rack_rect.updateRect();
                    }
                }
            }

            if (t_params.washing_cabinet_rect) {
                let sub_rects = [shower_room_area_rect];
                if (t_params.toilet_rect) {
                    let t_w_rect = t_params.toilet_rect.clone();
                    sub_rects.push(t_w_rect);
                }

                let ans_polys = t_params.washing_cabinet_rect.substract_polygons(sub_rects);
                if (ans_polys[0]) {
                    let t_rect = ZRect.fromPoints(ans_polys[0].positions, t_params.washing_cabinet_rect.nor);
                    if (t_rect.w < min_cabinet_length + 100) {
                        return false;
                    }
                    t_params.washing_cabinet_rect.copy(t_rect);
                }

            }

            for (let key in t_params) {
                params[key].copy(t_params[key]);
            }

            return true;
        }

        const enlarge_diamond_shower_room = () => {
            if (!params.diamond_shower_room_rect) return true;
            let t_params: I_TinyWashingRoomLayoutParams = {};
            for (let key in params) {
                t_params[key] = params[key].clone();
            }
            let diamond_shower_room_rect = t_params.diamond_shower_room_rect;


            const ShowerDistLevel = [900, 1000, 1100, 1200, 1300];

            let target_val = -1;
            for (let val of ShowerDistLevel) {
                if (val > diamond_shower_room_rect.min_hh) {
                    target_val = val;
                    break;
                }
            }
            if (target_val < 0) return false;
            let tw = target_val;
            let back_center = diamond_shower_room_rect.backEdge.unprojectEdge2d({ x: diamond_shower_room_rect.w - tw / 2, y: 0 });
            diamond_shower_room_rect._w = tw;
            diamond_shower_room_rect._h = tw;
            diamond_shower_room_rect.back_center = back_center;
            diamond_shower_room_rect.updateRect();



            let toilet_rect = t_params.toilet_rect;
            let shower_room_area_rect = diamond_shower_room_rect.clone();

            if (toilet_rect) {
                let sub_rects = [shower_room_area_rect];
                if (t_params.washing_cabinet_rect) {
                    let t_w_rect = t_params.washing_cabinet_rect.clone();
                    t_w_rect._h += 200;
                    sub_rects.push(t_w_rect);
                }

                let ans_polys = toilet_rect.substract_polygons(sub_rects);
                if (ans_polys[0]) {
                    TRoomShape.optimizePoly(ans_polys[0]);
                    let t_rect = ZRect.fromPoints(ans_polys[0].positions, toilet_rect.nor);

                    if (t_rect.w < min_toilet_length) {
                        return false;
                    }
                    else {
                        toilet_rect.copy(t_rect);
                    }
                }

                if (toilet_rect) {
                    if (t_params.towel_rack_rect) {
                        t_params.towel_rack_rect.back_center = toilet_rect.back_center;
                        t_params.towel_rack_rect._w = Math.min(params.towel_rack_rect._w, toilet_rect.w);
                        t_params.towel_rack_rect.updateRect();
                    }
                }
            }

            if (t_params.washing_cabinet_rect) {
                let sub_rects = [shower_room_area_rect];
                if (t_params.toilet_rect) {
                    let t_w_rect = t_params.toilet_rect.clone();
                    sub_rects.push(t_w_rect);
                }

                let ans_polys = t_params.washing_cabinet_rect.substract_polygons(sub_rects);
                if (ans_polys[0]) {
                    let t_rect = ZRect.fromPoints(ans_polys[0].positions, t_params.washing_cabinet_rect.nor);
                    if (t_rect.w < min_cabinet_length + 100) {
                        return false;
                    }
                    t_params.washing_cabinet_rect.copy(t_rect);
                }

            }

            for (let key in t_params) {
                params[key].copy(t_params[key]);
            }

            return true;
        }

        if (params.one_shower_room_rect) {
            enlarge_one_shower_room();
            enlarge_one_shower_room();
        }
        if (params.diamond_shower_room_rect) {
            enlarge_diamond_shower_room();
            enlarge_diamond_shower_room();
            enlarge_diamond_shower_room();

        }


        for (let key in params) {
            let rect = params[key] as ZRect;
            let group_template = rect._attached_elements[TGroupTemplate.EntityName] as TGroupTemplate;
            if (group_template) {
                group_template.updateByTargetRect();
            }
        }

    }



    protected filterCandidates() {
        let be_contained_candidates: number[] = [];
        for (let i = 0; i < this._candidate_figure_list.length; i++) {
            if (be_contained_candidates.includes(i)) continue;
            for (let j = i + 1; j < this._candidate_figure_list.length; j++) {
                if (this.containGroupTemplates(this._candidate_figure_list[i].group_templates, this._candidate_figure_list[j].group_templates)) {
                    be_contained_candidates.push(j);
                }
            }
        }
        this._candidate_figure_list = this._candidate_figure_list.filter((candidate, index) => !be_contained_candidates.includes(index));
    }

    protected containGroupTemplates(group_templates0: TGroupTemplate[], group_tempaltes1: TGroupTemplate[]) {
        if (group_templates0.length < group_tempaltes1.length) return false;
        for (let j = 0; j < group_tempaltes1.length; j++) {
            let g0 = group_templates0[j];
            let g1 = group_tempaltes1[j];

            if (g0.group_space_category !== g1.group_space_category) return false;
            let c_p = g0._target_rect.is_simple_shape_equal_to(g1._target_rect, 200);

            if (!c_p) return false;
        }
        return true;
    }




}

// TODO 这个还是需要再改进下
function checkRectBackOnWindow(room: TRoom, rect: ZRect, tol: number = 5, ratio: number = 0.1): void
{
    let roomEdges: ZEdge[] = room.room_shape._poly.edges;
    let windows: I_Window[] = room.windows;
    let targetLayOnEdge: ZEdge = null;
    for(let i = 0; i < roomEdges.length; i++)
    {
        // TODO 这里需要再加一层判断此边是否有window
        let layonWindow: I_Window = null;
        for(let window of windows)
        {
            if(!TBaseRoomToolUtil.instance.edgeOnPolygon(roomEdges[i], window.rect, tol, ratio))
            {
                continue;
            }
            layonWindow = window;
            break;
        }
        if(!layonWindow || !TBaseRoomToolUtil.instance.edgeOnPolygon(rect.backEdge, layonWindow.rect, tol, ratio))
        {
            continue;
        }
        let preEdge = roomEdges[(i - 1 + roomEdges.length) % roomEdges.length];
        let nextEdge = roomEdges[(i + 1) % roomEdges.length];
        let preDist: number = Math.abs(preEdge.projectEdge2d(rect.rect_center).y);
        let nextDist: number = Math.abs(nextEdge.projectEdge2d(rect.rect_center).y);
        if(preDist < nextDist)
        {
            targetLayOnEdge = preEdge;
        }
        else
        {
            targetLayOnEdge = nextEdge;
        }
        break;
    }
    if(!targetLayOnEdge)
    {
        return;
    }
    let oldRectCenter = rect.rect_center.clone();
    let projectPoint: any = targetLayOnEdge.projectEdge2d(oldRectCenter);
    // 检查此投影x方向上是否正确地落在此线上没有超出范围
    if(projectPoint.x > 0 && projectPoint.x < targetLayOnEdge.length)
    {
        projectPoint.y = -rect.h / 2;
        let newCenterPoint: Vector3 = targetLayOnEdge.unprojectEdge2d(projectPoint);
        rect.nor = targetLayOnEdge.nor.clone().negate();
        rect.rect_center = newCenterPoint;
    }
}

function isLayonWindows(edge: ZEdge, windows: I_Window[], tol: number = 10): boolean{
    for(let window of windows)
    {
        if(TBaseRoomToolUtil.instance.edgeOnPolygon(edge, window.rect, tol, 0.1))
        {
            return true;
        }
    }
    return false;
}

function isCanPutDiamondShowerRoom(showerHeadRect: ZRect, otherBlockRects: ZRect[], innerRects: ZRect[]): boolean
{
    // 1. 确认前房存在空的邻接格子
    let emptyGridRects: ZRect[] = innerRects.filter(rect => 
        {
            let isValid: boolean = true;
            for(let blockRect of [showerHeadRect, ...otherBlockRects]){
                if(!blockRect)
                {
                    continue;
                }
                if(TBaseRoomToolUtil.instance.isOverlayByRects(blockRect, rect))
                {
                    isValid = false;
                    break;
                }
            }
            return isValid;
        });
    for(let emptyRect of emptyGridRects)
    {
        let subVec: Vector3 = emptyRect.rect_center.clone().sub(showerHeadRect.rect_center).normalize();
        if(subVec.dot(showerHeadRect.nor) > 0.6 && TBaseRoomToolUtil.instance.isLayOnPolygons(emptyRect, showerHeadRect, null, 0.2, 100))
        {
            return true;
        }
    }
    return false;
}

function isOneShowerRoomInnerExistWashCabinerOrToilet(oneShowerRect: ZRect, otherBlockRects: ZRect[]): boolean
{
    for(let otherBlockRect of otherBlockRects)
    {
        let subVec: Vector3 = otherBlockRect.rect_center.clone().sub(oneShowerRect.rect_center).normalize();
        if(oneShowerRect.nor.dot(subVec) > 0)
        {
            return true;
        }
    }
    return false;
}

function filterShowerHeadCandidateGrids(sourceRects: ZRect[], innerGridRects: ZRect[], doorRects: ZRect[]): ZRect[]
{
    let validRects: ZRect[] = [];
    let minXIndex: number = null;
    let maxXIndex: number = null;
    let minYIndex: number = null;
    let maxYIndex: number = null;
    for(let innerRect of innerGridRects)
    {
        if(minXIndex == null || innerRect._attached_elements.GridPosId.x < minXIndex)
        {
            minXIndex = innerRect._attached_elements.GridPosId.x;
        }
        if(maxXIndex == null || innerRect._attached_elements.GridPosId.x > maxXIndex)
        {
            maxXIndex = innerRect._attached_elements.GridPosId.x;
        }
        if(minYIndex == null || innerRect._attached_elements.GridPosId.y < minYIndex)
        {
            minYIndex = innerRect._attached_elements.GridPosId.y;
        }
        if(maxYIndex == null || innerRect._attached_elements.GridPosId.y > maxYIndex)
        {
            maxYIndex = innerRect._attached_elements.GridPosId.y;
        }
    }
    for (let sourceRect of sourceRects)
    {
        if((sourceRect._attached_elements.GridPosId.x == minXIndex || sourceRect._attached_elements.GridPosId.x == maxXIndex) && (sourceRect._attached_elements.GridPosId.y == minYIndex || sourceRect._attached_elements.GridPosId.y == maxYIndex))
        {
            let nearDist: number = null;
            for(let doorRect of doorRects)
            {
                let  dist = TBaseRoomToolUtil.instance.calDistanceByPolygons(sourceRect, doorRect);
                if(nearDist == null || dist < nearDist)
                {
                    nearDist = dist;
                }
            }
            if(nearDist > GridLength / 2)
            {
                validRects.push(sourceRect);
            }
        }
    }
    if(validRects.length == 0)
    {
        validRects = sourceRects;
    }
    return validRects;
}

function isEdgeInGrid(edge: ZEdge, rect: ZRect): boolean
{
    let edgeRange: any = TBaseRoomToolUtil.instance.getRange2dByEdge(edge);
    let rectRange: any = TBaseRoomToolUtil.instance.getRange2dByPolygon(rect);
    if(TBaseRoomToolUtil.instance.isOverlayRange2ds(edgeRange, rectRange))
    {
        return true;
    }
    return false;
}

function isLayonValidect(sourceEdge: ZEdge, sourceRect: ZRect, params: I_TinyWashingRoomLayoutParams): boolean
{
    let validRects: ZRect[] = [];
    for(let innerRect of params.inner_grid_rects)
    {
        let  isValid: boolean = true;
        for(let blockRect of [params.grid_0, params.grid_1, params.grid_2])
        {
            if(!blockRect)
            {
                continue;
            }
            if(blockRect == innerRect)
            {
                isValid = false;
                break;
            }
        }
        if(isValid)
        {
            validRects.push(innerRect);
        }
    }
    for(let validRect of validRects)
    {
        if(TBaseRoomToolUtil.instance.edgeOnPolygon(sourceEdge, validRect, 10, 0.1))
        {
            return true;
        }
    }
    return false;
}
