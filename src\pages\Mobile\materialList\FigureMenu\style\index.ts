
import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    figure: css`
      display: grid;
      max-height: 224px;
      /* padding-top: 16px; */
      /* transition: all .3s; */
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0 16px;
      margin-bottom: 16px;
      grid-template-columns: repeat(6, 1fr);
      grid-template-rows: repeat(2, 1fr);
      grid-auto-rows: 1fr;
      gap: calc((4vw - 16px) / 6);
      padding-bottom: 30px;
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
      }
      @media screen and (max-width: 450px) { // 手机宽度
        grid-template-columns: repeat(4, 1fr);
      }
      @media screen and (orientation: landscape) {
        grid-template-columns: repeat(2, 1fr);
        max-height: calc(var(--vh, 1vh) * 100 - 280px);
      }
      @media screen and (orientation: landscape) and (max-width: 960px) {
        min-height: calc(var(--vh, 1vh) * 100 - 190px);
      }
      .fold
      {
        width: 100%;
        border-radius: 4px;
        background: #F4F5F5;
        height: 24px;
        line-height: 24px;
        font-weight: 600;
        padding: 0 5px;
        margin: 8px 0 4px 0;
      }
      // .content {
      //   display: flex;
      //   flex-wrap: nowrap;
      //   transition: max-height 0.3s ease-in-out; /* 这将添加过渡动画 */
      // }

      // .collapsed {
      //   max-height: 0;
      // }
      .item {
        /* max-width: 30%; */
        margin: 1vw 0;
        width: 100%;
        height: 16vw;
        cursor: pointer;
        transition: box-shadow 0.3s ease;
        user-select:none;
        @media screen and (max-width: 450px) { // 手机宽度
          height: 28vw;
        }
        @media screen and (orientation: landscape) {
          height: 130px;
        }
        :nth-child(6n) {
          margin-right: 0;
        }
        .image {
          height: calc(15vw * 0.9);
          width: 90%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          overflow:hidden;
          /* transition: all .3s; */
          background-color: #f5f5f5;
          @media screen and (max-width: 450px) { // 手机宽度
            height: calc(25vw * 0.9);
          }
          @media screen and (orientation: landscape) {
            height: 100%;
            width: 100%;
          }
          .ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
          }
          .structure-image.ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
            height: 60px;
            width: 60px;
          }
          .group_image.ant-image-img {
            vertical-align: middle;
            user-select:none;
            pointer-events: none; 
          }

        }

        .title {
          color: #000000;
          font-size: 12px;
          padding: 5px 0;
          height: 40px;
          line-height: 20px;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .item:hover {
          /* box-shadow: 3px 3px 8px 0px rgba(0, 0, 0, 0.12); */
        }
    `,
    mobile: css`
      /* height: calc(100vh - 370px) !important; */
    `
  };
});
