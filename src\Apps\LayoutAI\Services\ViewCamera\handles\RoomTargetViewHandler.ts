import { ViewCameraRuler } from "../ViewCameraRuler";
import {
    TViewCameraEntity,
    IViewCameraGenerateOptions
} from "@/Apps/LayoutAI/Layout/TLayoutEntities/TExtDrawingElements/TViewCameraEntity";
import { BaseViewHandler } from "./BaseViewHandler";
import { compareNames, ZRect } from "@layoutai/z_polygon";
import { TFurnitureEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TFurnitureEntity";
import { TRoomEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TRoomEntity";
import { TSubSpaceAreaEntity } from "@/Apps/LayoutAI/Layout/TLayoutEntities/TSubSpaceAreaEntity";
import { IType2UITypeDict } from "@/Apps/LayoutAI/Layout/IRoomInterface";

export class RoomTargetViewHandler extends BaseViewHandler {
    handle(ruler: ViewCameraRuler, roomEntity: TRoomEntity, options: IViewCameraGenerateOptions ): TViewCameraEntity[] {
        let entities: TViewCameraEntity[] = [];
        if (this.checkCondition(ruler, roomEntity)) {
            let targetList = (ruler.target as string).trim().split("|");
            let furnitureList = roomEntity.getFurnitureEntitiesOnFlat();
            // 若当前图元不存在则判断下一个
            for (let target of targetList) {
                for (let furniture of furnitureList) { 
                    if (furniture.category !== target) { 
                        continue; 
                    } 
                    let entity: TViewCameraEntity; 
                    if (ruler.condition?.spaceArea) { 
                        let areasEntities = roomEntity._sub_room_areas as TSubSpaceAreaEntity[]; 
                        areasEntities.forEach(areaEntity => { 
                            if (IType2UITypeDict[areaEntity.space_area_type] === ruler.condition.spaceArea ) {
                                entity = this.createTargetView(
                                    ruler,
                                    options,
                                    furniture,
                                    roomEntity,
                                    areaEntity
                                );
                            }
                        });
                    } else {
                        entity = this.createTargetView(ruler, options, furniture, roomEntity);
                    }
                    entities.push(entity);
                    break;
                }
            }
        }
        return entities;
    }

    private createTargetView(
        ruler: ViewCameraRuler,
        options: IViewCameraGenerateOptions,
        furniture: TFurnitureEntity,
        roomEntity: TRoomEntity,
        areaEntity?: TSubSpaceAreaEntity
    ): TViewCameraEntity {
        let pos = { x: 0, y: 0, z: 0 };
        if (areaEntity) {
            pos = this.getViewCameraPos(ruler, areaEntity);
        } else {
            pos = this.getViewCameraPos(ruler, roomEntity);
        }
        const furnitureRect = furniture.matched_rect || furniture.rect;
        let rect = new ZRect(500, 500);
        rect.nor = furnitureRect.nor.clone().negate();
        rect.rect_center = pos;
        const target = [furniture.category];
        let entity = this.createViewCameraEntity(ruler, options, rect, target, roomEntity);
        return entity;
    }
}
