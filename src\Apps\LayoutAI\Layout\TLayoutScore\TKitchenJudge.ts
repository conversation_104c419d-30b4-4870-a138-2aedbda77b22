import { compareNames } from "@layoutai/z_polygon";
import { UI_FormatType, UI_Grade } from "../IUIInterface";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TGraphBasicConfigs } from "../TLayoutGraph/TGraphBasicConfigs";
import { TLayoutKitchenRoomRuleParamConfiguration } from "../TLayoutScoreConfigurationTool/TLayoutKitchenRoomRuleParamConfiguration";
import { TLayoutScoreParamName } from "../TLayoutScoreConfigurationTool/TLayoutScoreParamName";
import { TRoom } from "../TRoom";
import { TElementAreaCheckRule } from "./CheckRules/AreaCheckRules/TElementAreaCheckRule";
import { TBaseWindowOrDoorOcclusionCheckRule } from "./CheckRules/BasicCheckRules/TBaseWindowOrDoorOcclusionCheckRule";
import { TDistanceCheckRule } from "./CheckRules/kitchenRoomCheckRules/TDistanceCheckRule";
import { TDistanceWithCookingCheckRule } from "./CheckRules/kitchenRoomCheckRules/TDistanceWithCookingCheckRule";
import { TDistanceWithFlueCheckRule } from "./CheckRules/kitchenRoomCheckRules/TDistanceWithFlueCheckRule";
import { TFigureOnWidnowCheckRule } from "./CheckRules/kitchenRoomCheckRules/TFigureOnWidnowCheckRule";
import { TFigureShapecheckRule } from "./CheckRules/kitchenRoomCheckRules/TFigureShapeCheckRule";
import { TFigureWithWallCheckRule } from "./CheckRules/kitchenRoomCheckRules/TFigureWithWallCheckRule";
import { TKitchenCheckFunctionRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenCheckFunctionRule";
import { TKitchenCutPlaneCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenCutPlaneCheckRule";
import { TKitchenCheckFlowLineCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenFlowLineCheckRule";
import { TKitchenFlueCookingBeamCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenFlueCookingBeamCheckRule";
import { TKitchenFridgeCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenFridgeChekRule";
import { TKitchenPutPlaneCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenPutPlaneCheckRule";
import { TKitchenToolUtil } from "./CheckRules/kitchenRoomCheckRules/TKitchenToolUtil";
import { TKitchenWorkingPlaneCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenWorkingPlaneCheckRule";
import { TKitchenWorkingTriangleCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenWorkingTriangleCheckRule";
import { TKitchenWorkingTriangleFlowCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenWorkingTriangleFlowCheckRule";
import { TMaxDistanceInFlatPlaneCheckRule } from "./CheckRules/kitchenRoomCheckRules/TMaxDistanceInFlatPlaneCheckRule";
import { FigureShapeType, RoomType } from "./CheckRules/TCheckRule";
import { TGroupCheckRule } from "./CheckRules/TGroupCheckRule";
import { I_LayoutScore, TLayoutJudge } from "./TLayoutJudge";
import { TKitchenAreaCornerCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenAreaCornerCheckRule";
import { TKitchenAreaNeighborCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenAreaNeighborCheckRule";
import { TKitchenAreaOpCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenAreaOpCheckRule";
import { TKitchenAreaOrderCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenAreaOrderCheckRule";
import { TKitchenAreaWinCheckRule } from "./CheckRules/kitchenRoomCheckRules/TKitchenAreaWinCheckRule";
import { TRoomLayoutScheme } from "../TLayoutScheme/TRoomLayoutScheme";

export class TKitchenJudge extends TLayoutJudge {
    static TriangleMovingLine = "三角动线合理性"; // 有灶具、水槽60分; 有冰箱80分; 动线三角长度超出合理范围：扣20分;
    static CabinetAreaName = "收纳区域面积"; //  空间利用率得分

    static HoodCookerAlignment = "烟机灶具对齐度"; // 准出条件, 不能超出300mm;

    constructor() {
        super();
        this.name = "TKitchenJudge";
        this.ruleParamConfig = new TLayoutKitchenRoomRuleParamConfiguration();
        let basic_layout_rule_group = new TGroupCheckRule(
            TLayoutJudge.BasicScore,
            [
                // 炉灶靠窗, 这个可能后面还是需要进行更改的
                new TFigureOnWidnowCheckRule(TKitchenToolUtil.instance.cookingFigureNames, {
                    ruleName: "炉灶靠窗",
                    parentJudge: this,
                    figureScoreFunc: (score: number) => {
                        let resultScore: number = score ? -100 : 0;
                        return resultScore;
                    }
                }),

                new TDistanceWithFlueCheckRule(TKitchenToolUtil.instance.cookingFigureNames, {
                    ruleName: "炉灶与烟道的距离",
                    computedTotalScore: true,
                    parentJudge: this,
                    figureScoreFunc: score => {
                        if (!score) {
                            return 0;
                        }
                        let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                            TLayoutScoreParamName.basicGroup,
                            [TLayoutScoreParamName.distanceWithFlueRule],
                            score
                        );
                        return resultScore;
                    }
                }),
                new TDistanceWithCookingCheckRule(
                    [
                        ...TKitchenToolUtil.instance.cookingFigureNames,
                        ...TKitchenToolUtil.instance.fridgeFigureNames
                    ],
                    {
                        ruleName: "炉灶与冰箱的距离",
                        extand_distance: 0,
                        computedTotalScore: true,
                        parentJudge: this,
                        figureScoreFunc: score => {
                            if (!score) {
                                return 0;
                            }
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [TLayoutScoreParamName.distanceWithCookingRule],
                                score
                            );
                            return resultScore;
                        }
                    },
                    TKitchenToolUtil.instance.cookingName,
                    TKitchenToolUtil.instance.fridgeName
                ),

                new TFigureWithWallCheckRule(TKitchenToolUtil.instance.cookingFigureNames, {
                    ruleName: "炉灶到墙的距离",
                    roomType: RoomType.k_kitchenRoom,
                    parentJudge: this,
                    figureScoreFunc: score => {
                        if (!score) {
                            return 0;
                        }
                        let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                            TLayoutScoreParamName.basicGroup,
                            [TLayoutScoreParamName.cookingWithWallRule],
                            score
                        );
                        return resultScore;
                    }
                }),
                new TKitchenFlueCookingBeamCheckRule(
                    [...TKitchenToolUtil.instance.cookingFigureNames],
                    {
                        ruleName: "排烟管道绕梁",
                        computedTotalScore: true,
                        parentJudge: this,
                        figureScoreFunc: (score: number) => {
                            let overBeamScore: number = this.ruleParamConfig.getValueByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [
                                    TLayoutScoreParamName.kitchenFlueCookingBeamRule,
                                    TLayoutScoreParamName.kitchenFlueCookingOverBeamScore
                                ]
                            );
                            let withoutOverBeamScore: number =
                                this.ruleParamConfig.getValueByParamConfig(
                                    TLayoutScoreParamName.basicGroup,
                                    [
                                        TLayoutScoreParamName.kitchenFlueCookingBeamRule,
                                        TLayoutScoreParamName.kitchenFlueCookingWithoutOverBeamScore
                                    ]
                                );
                            return score > 0 ? overBeamScore : withoutOverBeamScore;
                        }
                    }
                ),

                new TFigureOnWidnowCheckRule(TKitchenToolUtil.instance.rinseFigureNames, {
                    ruleName: "水槽靠窗",
                    parentJudge: this,
                    figureScoreFunc: (score: number) => {
                        let rinseNearWindowScore: number =
                            this.ruleParamConfig.getValueByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [
                                    TLayoutScoreParamName.rinseOnWidnowRule,
                                    TLayoutScoreParamName.rinseNearWindowScore
                                ]
                            );
                        let rinseNotNearWindowScore: number =
                            this.ruleParamConfig.getValueByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [
                                    TLayoutScoreParamName.rinseOnWidnowRule,
                                    TLayoutScoreParamName.rinseNotNearWindowScore
                                ]
                            );
                        let resultScore: number = score
                            ? rinseNearWindowScore
                            : rinseNotNearWindowScore;
                        return resultScore;
                    }
                }),

                new TDistanceCheckRule(
                    [
                        ...TKitchenToolUtil.instance.rinseFigureNames,
                        ...TKitchenToolUtil.instance.fridgeFigureNames
                    ],
                    {
                        ruleName: "水槽与冰箱的距离",
                        computedTotalScore: true,
                        figureScoreFunc: score => {
                            if (score < 0) {
                                return 0;
                            }
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [TLayoutScoreParamName.fridgeWithRinseDistanceRule],
                                score
                            );
                            return resultScore;
                        }
                    },
                    TKitchenToolUtil.instance.rinseName,
                    TKitchenToolUtil.instance.fridgeName
                ),

                new TKitchenCheckFunctionRule(
                    [
                        ...TKitchenToolUtil.instance.cookingFigureNames,
                        ...TKitchenToolUtil.instance.rinseFigureNames,
                        ...TKitchenToolUtil.instance.flatPlaneFigureNames
                    ],
                    {
                        ruleName: "必备家具: 炉灶，水槽，操作台面",
                        computedTotalScore: true,
                        figureScoreFunc: score => {
                            let resultScore: number = score > 0.9 ? 20 : -20;
                            return resultScore;
                        }
                    }
                ),

                new TKitchenFridgeCheckRule(TKitchenToolUtil.instance.fridgeFigureNames, {
                    ruleName: "具备冰箱",
                    computedTotalScore: true,
                    figureScoreFunc: score => {
                        let hasFridgeScore: number = this.ruleParamConfig.getValueByParamConfig(
                            TLayoutScoreParamName.basicGroup,
                            [
                                TLayoutScoreParamName.kitchenFridgeRule,
                                TLayoutScoreParamName.hasFridgeScore
                            ]
                        );
                        let withoutFridgeScore: number = this.ruleParamConfig.getValueByParamConfig(
                            TLayoutScoreParamName.basicGroup,
                            [
                                TLayoutScoreParamName.kitchenFridgeRule,
                                TLayoutScoreParamName.withoutFridgeScore
                            ]
                        );
                        let resultScore: number = score != 1 ? withoutFridgeScore : hasFridgeScore;
                        return resultScore;
                    }
                }),

                // // TODO 布局形状
                // new TFigureShapecheckRule(TKitchenToolUtil.instance.flatPlaneFigureNames,{
                //     ruleName: "布局形状",
                //     computedTotalScore: true,
                //     figureScoreFunc: (score) => {
                //         let resultScore: number = 0;
                //         let IShapeScore: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.figureShapeRule, TLayoutScoreParamName.IShapeScore]);
                //         let IIShapeScore: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.figureShapeRule, TLayoutScoreParamName.IIShapeScore]);
                //         let LShapeScore: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.figureShapeRule, TLayoutScoreParamName.LShapeScore]);
                //         let UShapeScore: number = this.ruleParamConfig.getValueByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.figureShapeRule, TLayoutScoreParamName.UShapeScore]);
                //         if(score & FigureShapeType.k_IShape)
                //         {
                //             resultScore += IShapeScore;
                //         }
                //         if(score & FigureShapeType.k_IIShape)
                //         {
                //             resultScore += IIShapeScore;
                //         }
                //         if(score & FigureShapeType.k_LShape)
                //         {
                //             resultScore += LShapeScore;
                //         }
                //         if(score & FigureShapeType.k_UShape)
                //         {
                //             resultScore += UShapeScore;
                //         }
                //         return resultScore;
                //     }
                // }),

                // TODO 门窗干涉, 这个主要是计算得分情况
                // 家具挡推拉门，这个和卧室一样
                // new TBaseWindowOrDoorOcclusionCheckRule(TGraphBasicConfigs.KitchenCabinetsCategories, {
                //         ruleName: "布局挡门(推拉门)",
                //         computedTotalScore: true,
                //         otherExtendInfo: {isWindow: false, isGetScore: false},
                //         figureScoreFunc: (score) => {
                //             let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(TLayoutScoreParamName.basicGroup, [TLayoutScoreParamName.slidingDoorOcclusionRule], score);
                //             return resultScore;
                //         }
                //     }),

                // 家具挡窗, 比分规则和上述一样
                // 这里计算挡窗是计算挡窗面积，这里重新进行编写
                new TBaseWindowOrDoorOcclusionCheckRule(
                    TGraphBasicConfigs.KitchenCabinetsCategories,
                    {
                        ruleName: "家具挡窗",
                        computedTotalScore: true,
                        otherExtendInfo: { isWindow: true, isGetScore: false },
                        figureScoreFunc: (score: number) => {
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.basicGroup,
                                [TLayoutScoreParamName.kitchenWindowOcclusionRule],
                                score
                            );
                            return resultScore;
                        }
                    }
                ),

                // 橱柜的连续性
                new TMaxDistanceInFlatPlaneCheckRule(
                    TKitchenToolUtil.instance.flatPlaneFigureNames,
                    {
                        ruleName: "地柜连续性",
                        computedTotalScore: true,
                        figureScoreFunc(score) {
                            let resultScore: number = score;
                            return resultScore;
                        }
                    }
                )

                // TODO 新增素材互相干涉
                // new TFiguresOverlay(TGraphBasicConfigs.KitchenCabinetsCategories, {
                //     ruleName: "素材互相干涉",
                //     computedTotalScore: true,
                //     figureScoreFunc: (score: number) => {
                //         return score > 0 ? -100 : 0;
                //     }
                // }),
            ],
            {
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                gradeFunc: (score, value) => {
                    if (score < 0) {
                        return UI_Grade.GradeE;
                    } else if (score < 70 && score > 0) {
                        return UI_Grade.GradeB;
                    } else {
                        return UI_Grade.GradeAp;
                    }
                }
            }
        );

        // TODO 布局动线
        let basic_traffic_rule_group = new TGroupCheckRule(
            TLayoutJudge.Flowline,
            [
                // TODO 过道判断
                new TKitchenCheckFlowLineCheckRule(TGraphBasicConfigs.KitchenCabinetsCategories, {
                    ruleName: "过道最短距离",
                    ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                    parentJudge: this,
                    roomScoreFunc: (score: number) => {
                        return score;
                    },
                    computedTotalScore: true
                }),

                new TKitchenWorkingTriangleCheckRule(
                    [
                        ...TKitchenToolUtil.instance.cookingFigureNames,
                        ...TKitchenToolUtil.instance.takeFigureNames,
                        ...TKitchenToolUtil.instance.flatPlaneFigureNames,
                        ...TKitchenToolUtil.instance.rinseFigureNames
                    ],
                    {
                        ruleName: "三角动线平均长度",
                        computedTotalScore: true,
                        parentJudge: this,
                        figureScoreFunc: (score: number) => {
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.flowGroup,
                                [TLayoutScoreParamName.kitchenWorkingTriangleRule],
                                score
                            );
                            return resultScore;
                        }
                    }
                ),

                // 布局流畅性
                new TKitchenWorkingTriangleFlowCheckRule(
                    [
                        ...TKitchenToolUtil.instance.cookingFigureNames,
                        ...TKitchenToolUtil.instance.takeFigureNames,
                        ...TKitchenToolUtil.instance.flatPlaneFigureNames,
                        ...TKitchenToolUtil.instance.rinseFigureNames
                    ],
                    {
                        ruleName: "动线流畅性",
                        computedTotalScore: true,
                        parentJudge: this,
                        figureScoreFunc: (score: number) => {
                            return score;
                        }
                    }
                )
            ],
            {
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                gradeFunc: (score, value) => {
                    if (score < 0) {
                        return UI_Grade.GradeE;
                    } else if (score < 70 && score > 0) {
                        return UI_Grade.GradeB;
                    } else {
                        return UI_Grade.GradeAp;
                    }
                }
            }
        );

        // 收纳
        let basic_intake_rule = new TElementAreaCheckRule(
            TKitchenToolUtil.instance.flatPlaneFigureNames,
            {
                ruleName: TLayoutJudge.StorageScore,
                ui_format: [UI_FormatType.Name, UI_FormatType.Percentage, UI_FormatType.Grade],
                parentJudge: this,
                roomScoreFunc: (score: number, room: TRoom) => {
                    let ratio = score / room.room_shape._area;

                    let scoreII = Math.round((Math.min(ratio, 0.5) / 0.5) * 20);
                    return scoreII;
                },
                gradeFunc: (score, value) => {
                    // console.log(score);
                    if (score > 19) {
                        return UI_Grade.GradeAp;
                    } else if (score > 12) {
                        return UI_Grade.GradeA;
                    } else if (score > 8) {
                        return UI_Grade.GradeAm;
                    } else {
                        return UI_Grade.GradeE;
                    }
                }
            }
        );

        let flat_layout_rule_group = new TGroupCheckRule(
            TLayoutJudge.FlatPlaneScore,
            [
                new TKitchenWorkingPlaneCheckRule(TKitchenToolUtil.instance.flatPlaneFigureNames, {
                    ruleName: "操作台面",
                    computedTotalScore: true,
                    figureScoreFunc: (score: number) => {
                        let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                            TLayoutScoreParamName.flatPlaneGroup,
                            [TLayoutScoreParamName.kitchenWorkingPlaneRule],
                            score
                        );
                        return resultScore;
                    }
                }),

                new TKitchenCutPlaneCheckRule(
                    [
                        ...TKitchenToolUtil.instance.rinseFigureNames,
                        ...TKitchenToolUtil.instance.flatPlaneFigureNames
                    ],
                    {
                        ruleName: "黄金备菜区",
                        computedTotalScore: true,
                        figureScoreFunc: (score: number) => {
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.flatPlaneGroup,
                                [TLayoutScoreParamName.kitchenCutPlaneRule],
                                score
                            );
                            return resultScore;
                        }
                    }
                ),

                new TKitchenPutPlaneCheckRule(
                    [
                        ...TKitchenToolUtil.instance.cookingFigureNames,
                        ...TKitchenToolUtil.instance.flatPlaneFigureNames
                    ],
                    {
                        ruleName: "出餐区",
                        computedTotalScore: true,
                        figureScoreFunc: (score: number) => {
                            let resultScore: number = this.ruleParamConfig.getScoreByParamConfig(
                                TLayoutScoreParamName.flatPlaneGroup,
                                [TLayoutScoreParamName.kitchenPutPlaneRule],
                                score
                            );
                            return resultScore;
                        }
                    }
                )
            ],
            {
                ui_format: [UI_FormatType.Name, UI_FormatType.Stars],
                gradeFunc: (score, value) => {
                    if (score < 0) {
                        return UI_Grade.GradeE;
                    } else if (score < 70 && score > 0) {
                        return UI_Grade.GradeB;
                    } else {
                        return UI_Grade.GradeAp;
                    }
                }
            }
        );

        let split_space_rule_group = new TGroupCheckRule(TLayoutJudge.SplitSpaceScore, [
            new TKitchenAreaCornerCheckRule([], {
                ruleName: "转角禁区(水、火)",
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                }
            }),
            new TKitchenAreaNeighborCheckRule([], {
                ruleName: "不相邻约束",
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                }
            }),
            new TKitchenAreaOpCheckRule([], {
                ruleName: "操作区相邻约束",
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                }
            }),
            new TKitchenAreaOrderCheckRule([], {
                ruleName: "最优顺序",
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                }
            }),
            new TKitchenAreaWinCheckRule([], {
                ruleName: "窗下禁区",
                computedTotalScore: true,
                parentJudge: this,
                figureScoreFunc: (score: number) => {
                    return score;
                }
            })
        ]);

        this._check_rules = [
            basic_intake_rule,
            // basic_traffic_rule_group,
            // flat_layout_rule_group,
            basic_layout_rule_group,
            split_space_rule_group
        ];
    }

    computeScoreInRoom(
        room: TRoom,
        figure_elements: TFigureElement[] = null,
        layout_scheme: TRoomLayoutScheme = null
    ): I_LayoutScore[] {
        if (!room) return [];
        if (compareNames([room.roomname], ["厨房"]) == 0) return [];

        // let area_of_cabinets : number = 0;

        // for(let ele of figure_elements)
        // {
        //     if(compareNames([ ele.category],["地柜","吊柜"]))
        //     {
        //         area_of_cabinets += ele.rect.w / 1000 * ele.rect.h / 1000;
        //     }
        // }
        let result: I_LayoutScore[] = [];
        this._check_rules.forEach((rule, index) => {
            result.push(rule.computeLayoutScore(room, figure_elements, index, layout_scheme));
        });
        // let ans : I_LayoutScore[] = [];
        // ans.push({name:TKitchenJudge.CabinetAreaName,value:area_of_cabinets,order:0,count:1,score:area_of_cabinets});
        return result;
    }
}
