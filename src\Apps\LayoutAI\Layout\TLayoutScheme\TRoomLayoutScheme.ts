import { ZRect } from "@layoutai/z_polygon";
import { TFeatureShape } from "../TFeatureShape/TFeatureShape";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { I_FigureList, TFigureList } from "../TFigureElements/TFigureList";
import { TGroupTemplate } from "../TLayoutGraph/TGroupTemplate/TGroupTemplate";
import { TLayoutGraph } from "../TLayoutGraph/TLayoutGraph";
import { TCollisionJudge } from "../TLayoutScore/TCollisionJudge";
import { I_LayoutScore, TLayoutJudgeContainter } from "../TLayoutScore/TLayoutJudge";
import { I_SubSpacePoly, TRoom } from "../TRoom";
import { TGraphBasicConfigs } from "../TLayoutGraph/TGraphBasicConfigs";
import { I_Entity3D } from "../IRoomInterface";
import { compareNames } from "@layoutai/z_polygon";
import { LayoutAI_App } from "../../../LayoutAI_App";
import { TPainter } from "../../Drawing/TPainter";
import { Vector3 } from "three";
import SunvegaAPI from "@api/clouddesign";

export interface I_TLayoutScheme {
    figure_list: I_FigureList;

    _layout_scores?: I_LayoutScore[];

    _scheme_name?: string;
    _debug_data?: { [key: string]: any };
}

/**
 *  布局方案
 */
export class TRoomLayoutScheme {
    _q_id: number;
    figure_list: TFigureList; // 图元列表, 直接clone一份算了 ...

    group_templates: TGroupTemplate[];

    sub_area_list: I_SubSpacePoly[];

    room: TRoom;
    _feature_shape: TFeatureShape;

    _from_graph: TLayoutGraph; // 记录生成该方案的graph

    _layout_scores: I_LayoutScore[];

    private _layout_score_dict: { [key: string]: I_LayoutScore };

    _scheme_name: string;

    _debug_data: { [key: string]: any }; // 为方便测试, 给一个扩展内容

    _drawn_canvas: HTMLCanvasElement;

    _drawn_image: HTMLImageElement;
    totalScore: number;

    // 扩展内容
    extra: any;

    readonly IsRoomLayoutScheme: boolean = true;

    constructor() {
        this.figure_list = null;
        this.group_templates = [];
        this._scheme_name = "";
        this.room = null;
        this._feature_shape = null;
        this._layout_scores = [];
        this._layout_score_dict = {};
        this._debug_data = {};
        this._q_id = 0;
        this.totalScore = 0;
    }

    exportLayoutScore(score: I_LayoutScore) {
        let result_score: I_LayoutScore = { ...score };
        if (result_score.fineTuningFigures) delete result_score.fineTuningFigures;
        if (result_score.fineTuningTypes) delete result_score.fineTuningTypes;
        if (result_score.fineTuningValue) delete result_score.fineTuningValue;

        if (result_score.children) {
            result_score.children = result_score.children.map(data => this.exportLayoutScore(data));
        }
        return result_score;
    }

    exportScheme(option: { export_debug_data?: boolean } = {}): I_TLayoutScheme {
        return {
            figure_list: this.figure_list.exportData(),
            _layout_scores: this._layout_scores
                ? this._layout_scores.map(data => this.exportLayoutScore(data))
                : null,
            _scheme_name: this._scheme_name,
            _debug_data: option?.export_debug_data ? this._debug_data : null
        };
    }

    importScheme(data: I_TLayoutScheme) {
        this.figure_list = new TFigureList(data.figure_list);
        this._layout_scores = data._layout_scores || null;
        this._debug_data = data._debug_data || null;
        this._scheme_name = data._scheme_name || "";
        return this;
    }

    get layout_score_dict() {
        if (!this._layout_score_dict) {
            this._layout_score_dict = {};
        }
        for (let score of this._layout_scores) {
            this._layout_score_dict[score.name] = score;
        }
        return this._layout_score_dict;
    }
    checkIsValidByLayoutScores() {
        let scheme = this;
        let isValid = !Object.values(scheme.layout_score_dict).some(value => {
            if (value.children && value.children.length > 0) {
                return value.children.some(child => child.score <= -100);
            } else {
                return value.score <= -100;
            }
        });
        return isValid;
    }
    static checkIsValidByScores(scheme: I_TLayoutScheme) {
        let isValid = !scheme._layout_scores.some(value => {
            if (value.children && value.children.length > 0) {
                return value.children.some(child => child.score <= -100);
            } else {
                return value.score <= -100;
            }
        });
        return isValid;
    }
    // addTuringLayoutItem(layoutItem : I_TuringLayoutItem) {
    //     if (this.figure_list === null) {
    //         this.figure_list = new TFigureList();
    //     }

    //     let figure_elements = [TFigureElement.fromTuringLayoutItem(layoutItem)];

    //     if(figure_elements[0]?.modelLoc && figure_elements[0].modelLoc.endsWith("组合"))
    //     {
    //         const group_space_dict:{[key:string]:GroupSpaceCategory} ={
    //             "沙发组合": "沙发组合区",
    //             "餐桌椅组合": "餐桌区",
    //             "边柜组合":"餐边柜区",
    //             "鞋柜组合":"玄关柜区",
    //             "电视柜组合":"电视柜区",
    //             "餐边柜组合":"餐边柜区"
    //         }
    //         let t_code = group_space_dict[figure_elements[0].modelLoc];

    //         if(t_code)
    //         {
    //             let rect = figure_elements[0].rect.clone();
    //             let t_group_template = new TGroupTemplate();
    //             t_group_template.group_space_category = t_code;

    //             t_group_template._target_rect = rect;

    //             let use_rect_center = t_code ==="餐桌区";
    //             t_group_template.updateByTargetRect({use_rect_center:use_rect_center});
    //             if(!t_group_template?.current_s_group && compareNames([t_code],["餐桌区"],false))
    //             {
    //                 let r_center = rect.rect_center;
    //                 rect._h+=500;
    //                 rect.rect_center = r_center;
    //                 t_group_template.updateByTargetRect({use_rect_center:use_rect_center});

    //             }
    //             if(t_group_template?.current_s_group?.figure_elements)
    //             {
    //                 figure_elements = [...t_group_template.current_s_group.figure_elements];
    //             }

    //         }
    //         else{

    //         }
    //     }
    //     else if(compareNames([figure_elements[0].public_category],["浴室柜_马桶一体柜"],false)){
    //         return;
    //     }

    //     this.figure_list.figure_elements.push(...figure_elements);
    // }

    addFurnitureRect(furnitureRect: ZRect) {
        if (this.figure_list === null) {
            this.figure_list = new TFigureList();
        }
        this.figure_list.figure_elements.push(TFigureElement.fromCadFurnitureRect(furnitureRect));
    }

    addCustomCabinetModel(cabinet: SunvegaAPI.BasicBiz.CustomCabinetModel) {
        if (this.figure_list === null) {
            this.figure_list = new TFigureList();
        }
        this.figure_list.figure_elements.push(TFigureElement.fromCustomCabinetModel(cabinet));
    }

    computeScores(layout_graph: TLayoutGraph = null) {
        this._layout_scores = TLayoutJudgeContainter.ComputeSchemeScore(
            this.room || (this._feature_shape?._room as TRoom),
            this,
            layout_graph
        );
    }

    getLayoutScore(name: string) {
        return this._layout_score_dict[name] || null;
    }

    getLayoutScoreVal(name: string) {
        let l_score = this.getLayoutScore(name);
        if (!l_score) return 0;
        return l_score.score;
    }

    checkCollision(is_quiet: boolean = true) {
        let check_val: boolean = true;
        let min_val = 0;
        for (let figure_ele of this.figure_list.figure_elements) {
            if (
                this._from_graph &&
                this._from_graph._curr_feature_shape &&
                figure_ele.priority <= 1
            ) {
                let res = TCollisionJudge.checkCollision_rect_edges(
                    figure_ele.rect,
                    this._from_graph._curr_feature_shape._forbidden_area_edges
                );
                if (res.length > 0) {
                    let mval = 0;

                    for (let rr of res) {
                        if (rr.dval > mval) mval = rr.dval;
                    }

                    if (mval > min_val) {
                        min_val = mval;
                    }
                }
            }

            for (let ele of this.figure_list.figure_elements) {
                if (ele.priority < figure_ele.priority && figure_ele !== ele) {
                    let res = TCollisionJudge.checkCollision_rect_edges(
                        figure_ele.rect,
                        ele.rect.edges
                    );
                    if (ele.z_level < 0) continue;
                    if (figure_ele.z_level < 0) continue;
                    if (res.length > 0) {
                        let mval = 0;

                        for (let rr of res) {
                            if (rr.dval > mval) mval = rr.dval;
                        }
                        check_val = false;
                        if (!is_quiet) {
                            console.log(figure_ele.category, ele.category, res);
                        }

                        if (mval > min_val) {
                            min_val = mval;
                        }
                    }
                }
            }
        }

        // if(min_val < 500) return true;
        // else return false;

        return check_val;
    }

    recordFigures(figure_list: TFigureList) {
        this.figure_list = figure_list.clone();
    }
    /**
     *  完整性检查
     *        --- 如果没有配规则，则默认会true
     */
    checkIntegrityOnElements(room: TRoom) {
        if (!room?.roomname) return true;

        let rule = TGraphBasicConfigs.IntegrityRules[room.roomname];
        if (!rule) return true;

        let check_val = false;

        let target_categories: string[] = [];
        let figure_elements = this.figure_list.figure_elements;
        for (let g of figure_elements) {
            target_categories.push(g.category);
        }

        let rule_figure_categories = rule.figure_categories || [];
        let lack_of_categories: string[] = [];
        let target_categories_ans: string[] = [];

        for (let group_cates of rule_figure_categories) {
            let t_check_val = true;
            for (let cate of group_cates) {
                if (target_categories.indexOf(cate) < 0) {
                    t_check_val = false;

                    target_categories_ans = target_categories;
                    lack_of_categories = [cate];
                }
            }
            if (t_check_val) check_val = true;
        }
        if (!check_val) {
            LayoutAI_App.IsDebug &&
                console.log(
                    "布局" + this._scheme_name + "--- 完整性检查未通过",
                    room.uid,
                    room.roomname,
                    target_categories_ans,
                    lack_of_categories
                );
        }
        return check_val;
    }
    checkOcclusionOnElements(room: TRoom, quiet: boolean = false) {
        if (!room) return;
        let rules =
            TGraphBasicConfigs.OcclusionRules[room.roomname] ||
            TGraphBasicConfigs.OcclusionRules["Default"];
        if (!rules) return null;
        let figure_elements = this.figure_list.figure_elements;
        let occlusion_figure_elements: {
            ele: string;
            figure_name: string;
            figure_element: TFigureElement;
            wall_id?: number;
        }[] = [];
        // 默认全都不能穿墙
        for (let fig_element of figure_elements) {
            if (!room.room_shape._poly.containsPoint(fig_element.rect.rect_center, 200)) {
                occlusion_figure_elements.push({
                    ele: "room",
                    figure_name: fig_element.public_category,
                    figure_element: fig_element
                });
            }
        }
        for (let edge of room.room_shape._feature_shape._w_poly.edges) {
            let t_rect = new ZRect(edge.length, 10);
            t_rect.nor = edge.nor;

            //  给(10mm)一定误差容忍
            let tol = 65;
            t_rect.back_center = edge.center.clone().add(edge.nor.clone().multiplyScalar(tol));
            t_rect._w -= tol * 2;
            if (t_rect._w < 0) continue;

            t_rect.updateRect();

            for (let fig_element of figure_elements) {
                let fig_rect = fig_element.rect.clone();
                let intersect = t_rect.intersect(fig_rect);

                if (intersect && intersect.length > 0) {
                    occlusion_figure_elements.push({
                        ele: "Wall" + t_rect.nor.x + " " + t_rect.nor.y,
                        wall_id: edge._edge_id,
                        figure_name: fig_element.category,
                        figure_element: fig_element
                    });
                }
            }
        }

        let structure_elements: I_Entity3D[] = [
            ...room.windows,
            ...room.flues,
            ...room.pillars,
            ...room.pipes
        ];

        for (let ele of structure_elements) {
            if (ele.type === "Door") {
                if (!ele.realType || ele.realType === "Door") {
                    if (ele.length < 1800) {
                        ele.realType = "SingleDoor";
                    } else {
                        ele.realType = "SlidingDoor";
                    }
                }
            } else if (ele.type === "Window") {
                if (!ele.realType) {
                    ele.realType = "OneWindow";
                }
            }
            for (let t_rule of rules) {
                if (compareNames([ele.realType], t_rule.structure_types, true) == 1) {
                    let allow_figure_categories = t_rule.allow_figure_categories || [];
                    if (!ele.rect) continue;

                    let t_rect: ZRect = ele.rect.clone();

                    let r_center = t_rect.rect_center;
                    let t_l = t_rect.w;
                    let t_d = t_rect.h;
                    t_rect._w += eval(t_rule.occlusion_dir_extend || "0") * 2;
                    t_rect._h += eval(t_rule.occlusion_front_extend || "0") * 2;
                    if (t_rect.w < 0) continue;
                    t_rect.rect_center = r_center;

                    for (let fig_element of figure_elements) {
                        if (compareNames([fig_element.category], allow_figure_categories)) continue;

                        let target_rect = fig_element.rect.clone();

                        let intersect = t_rect.intersect(target_rect);
                        if (intersect.length > 0) {
                            occlusion_figure_elements.push({
                                ele: ele.realType,
                                figure_name: fig_element.public_category,
                                figure_element: fig_element
                            });
                        }
                    }
                }
            }
        }
        if (occlusion_figure_elements.length > 0 && !quiet) {
            //  LayoutAI_App.IsDebug && console.log("布局"+this._scheme_name+"--- 干涉检查未通过", room.uid, room.roomname, occlusion_figure_elements);
        }
        return occlusion_figure_elements || [];
    }

    checkBaseline_OnElements(room: TRoom, post_process_occlusion: boolean = true) {
        if (post_process_occlusion) {
            this.post_process_with_occlusion(room);
        } else {
            let occlusion_figure_elements = this.checkOcclusionOnElements(room);
            if (occlusion_figure_elements.length > 0) return false;
        }

        if (!this.checkIntegrityOnElements(room)) return false;

        return true;
    }

    post_process_with_occlusion(room: TRoom) {
        let occlusion_figure_elements = this.checkOcclusionOnElements(room, false);

        if (occlusion_figure_elements.length == 0) return false;

        // 先备份一份数据
        if (!this.figure_list._src_data) {
            this.figure_list._src_data = this.figure_list.clone() as any;
        }

        // 删掉那些有干涉的数据
        for (let ele of occlusion_figure_elements) {
            if (ele.figure_element) {
                this.figure_list.removeElement(ele.figure_element);
            }
        }

        return true;
    }
    static compareSameScheme(
        scheme0: TRoomLayoutScheme,
        scheme1: TRoomLayoutScheme,
        similar_tol: number = -1,
        ignore_categories: string[] = ["窗帘", "收口板", "地毯"]
    ) {
        let figure_elements0 = scheme0.figure_list?.figure_elements;
        let figure_elements1 = scheme1.figure_list?.figure_elements;

        if (!figure_elements0 || !figure_elements1) return -1;

        figure_elements0 = figure_elements0.filter(
            ele => !compareNames([ele.category], ignore_categories)
        );
        figure_elements1 = figure_elements1.filter(
            ele => !compareNames([ele.category], ignore_categories)
        );

        if (figure_elements0.length !== figure_elements1.length) return -1;

        const compareFigureElement = (a: TFigureElement, b: TFigureElement) => {
            let val = a.sub_category.localeCompare(b.sub_category);

            if (val == 0) {
                if (similar_tol < 0) {
                    let c_p = a.rect.is_simple_shape_equal_to(b.rect, similar_tol);
                    if (!c_p) {
                        return a.rect.area - b.rect.area;
                    } else {
                        return 0;
                    }
                }
            } else {
                return val;
            }
        };
        figure_elements0.sort(compareFigureElement);
        figure_elements1.sort(compareFigureElement);

        // 暂时只是比较完全相同

        for (let id in figure_elements0) {
            let fig0 = figure_elements0[id];
            let fig1 = figure_elements1[id];

            if (fig0.category !== fig1.category) return -1;

            let rect0 = fig0.rect;
            let rect1 = fig1.rect;

            if (!rect0.is_simple_shape_equal_to(rect1, similar_tol < 0 ? 10 : similar_tol)) {
                return -1;
            }
        }
        return 1;
    }

    static compareSchemeByScore(scheme0: TRoomLayoutScheme, scheme1: TRoomLayoutScheme) {
        return (
            scheme1.figure_list.figure_elements.length - scheme0.figure_list.figure_elements.length
        );
    }
    static appendSchemeToArray(
        scheme: TRoomLayoutScheme,
        arr: TRoomLayoutScheme[],
        pre_append: boolean = false,
        similar_tol: number = 600
    ) {
        let id = -1;
        for (let i = 0; i < arr.length; i++) {
            let t_scheme = arr[i];

            if (TRoomLayoutScheme.compareSameScheme(t_scheme, scheme, similar_tol) > 0) {
                id = i;
                break;
            }
        }

        if (id < 0) {
            if (pre_append) {
                let t_arr = [scheme, ...arr];
                arr.length = 0;
                arr.push(...t_arr);
            } else {
                arr.push(scheme);
            }
        }
    }

    static checkHasSameScheme(
        room: TRoom,
        furniture_list: TFigureElement[] = null,
        similar_tol: number = 300
    ) {
        let scheme0 = new TRoomLayoutScheme();

        furniture_list = furniture_list || room._furniture_list;
        let figure_elements = [...furniture_list];
        figure_elements.sort((a, b) => b.default_drawing_order - a.default_drawing_order);
        scheme0.figure_list = new TFigureList({
            target_room_names: room.roomname,
            figure_elements: figure_elements as any
        });
        scheme0._scheme_name = "待保存布局";
        scheme0.room = room;

        if (room._layout_scheme_list) {
            let ans_scheme = room._layout_scheme_list.find(scheme => {
                if (scheme._scheme_name.includes("DIY")) return false;
                if (scheme._scheme_name.includes("逻辑")) return false;
                if (scheme._scheme_name.includes("本地")) return false;
                // 只比较相似布局
                if (!scheme._scheme_name.includes("相似")) return false;

                let res = TRoomLayoutScheme.compareSameScheme(scheme0, scheme, similar_tol);
                console.log(scheme._scheme_name, scheme0._scheme_name);
                if (res == 1) {
                    return true;
                } else {
                    return false;
                }
            });
            return ans_scheme;
        }
        return null;
    }

    drawOnCanvas(
        painter: TPainter,
        canvasElement: HTMLCanvasElement,
        width: number = 400,
        height: number = 300
    ) {
        let currentRoom = this.room;
        if (!currentRoom) return;
        let previous_canvas = painter._canvas;
        let ts = painter.exportTransformData();

        painter.bindCanvas(canvasElement);
        painter.clean();
        canvasElement.width = width;
        canvasElement.height = height;
        painter.p_center = currentRoom.room_shape._poly.computeBBox().getCenter(new Vector3());
        let boxSize = currentRoom.room_shape._poly._boundingbox.getSize(new Vector3());
        let roomBoxWidth = boxSize.x;
        let roomBoxHeight = boxSize.y;
        let scaleW = canvasElement.width / roomBoxWidth;
        let scaleH = canvasElement.height / roomBoxHeight;
        painter._p_sc = (scaleW > scaleH ? scaleH : scaleW) * 0.85;
        currentRoom._painter = painter;
        painter.enter_drawpoly();
        this.drawScheme(painter, true);
        painter.leave_drawpoly();

        painter.bindCanvas(previous_canvas);
        painter.importTransformData(ts, false);
    }

    drawScheme(painter: TPainter, draw_wall: boolean) {
        if (draw_wall) {
            let currentRoom = this.room;
            if (currentRoom._room_entity && currentRoom) {
                currentRoom._room_entity.drawRoomWithWalls(painter);
            } else {
                let previous_painter = currentRoom._painter;
                currentRoom._painter = painter;
                currentRoom.drawRoomWithWalls(30);
                currentRoom.drawRoomWindows("Layout");
                currentRoom._painter = previous_painter;
            }
        }

        let figure_elements = [...this.figure_list.figure_elements];
        figure_elements.sort((a, b) => a.default_drawing_order - b.default_drawing_order);
        for (let ele of figure_elements) {
            if (ele._decoration_type) continue;
            //   if (compareNames([ele.sub_category],["吊柜"])) continue;
            ele.drawFigure(painter, false);
        }
    }
}
