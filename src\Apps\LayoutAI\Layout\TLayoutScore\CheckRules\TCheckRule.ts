import { compareNames } from "@layoutai/z_polygon";
import { IRoomEntityRealType } from "../../IRoomInterface";
import { UI_FormatType } from "../../IUIInterface";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { TRoom } from "../../TRoom";
import { I_LayoutScore, TLayoutJudge, TrimType } from "../TLayoutJudge";
import { TBaseRoomToolUtil } from "./BasicCheckRules/TBaseRoomToolUtil";
import { TRoomLayoutScheme } from "../../TLayoutScheme/TRoomLayoutScheme";

export enum RoomType {
    k_none = 0,
    k_kitchenRoom
}

export enum FigureShapeType {
    k_none = 0,
    k_IShape = 2,
    k_IIShape = 4,
    k_LShape = 8,
    k_UShape = 16
}

export interface I_CheckRuleOptions {
    weight?: number;

    figureScoreFunc?: (detailscore: number, figure?: TFigureElement) => number;

    roomScoreFunc?: (score: number, room?: TRoom) => number;

    percentFunc?: (value: number, room?: TRoom) => number;
    gradeFunc?: (score: number, value?: number) => number;
    continuityFunc?: (value: any) => number;
    /**
     *  背靠墙的距离
     */
    back_wall_distance?: number;
    /**
     *  床尾靠墙的距离
     */
    front_wall_distance?: number;
    /**
     *  侧靠墙的距离
     */
    side_wall_distance?: number;

    /**
     *  挡门范围
     */
    door_occlusion_distance?: number;

    /**
     *  挡门类型
     */
    target_door_realtypes?: IRoomEntityRealType[];

    /**
     *  挡窗范围
     */
    window_occlusion_distance?: number;
    /**
     *  可挡视线的家具
     */
    sight_occlusion_categories?: string[];

    main_ele_categories?: string[];

    ruleName?: string;

    scoreName?: string;

    parentName?: string;

    /**
     *  UI Format
     */
    ui_format?: UI_FormatType[];

    /**
     *  信息
     */
    info?: string;
    computedTotalScore?: boolean;
    /**
     *  扩展的距离，用于计算两个物体的距离
     */
    extand_distance?: number;

    roomType?: RoomType;

    otherExtendInfo?: any;

    parentJudge?: TLayoutJudge;

    /**
     *  是否时基础检查项
     */
    isBasicCheck?: boolean;

    /**
     * 指标数值加入到生成排序和过滤中
     */
    isSortAndFilter?: boolean;
}
export class TCheckRule {
    _figure_categories: string[];
    weight: number;

    _use_score_func: boolean = true;

    ruleName: string;

    scoreName?: string;

    parentName?: string;

    /**
     *  是否时基础检查项
     */
    isBasicCheck?: boolean;

    /**
     *  是否将评分指标加入生成排序中
     */
    isSortAndFilter?: boolean;

    /**
     *  UI Format
     */
    ui_format?: UI_FormatType[];

    figureScoreFunc: (detailscore: number, figure?: TFigureElement) => number;
    roomScoreFunc?: (score: number, room?: TRoom) => number;
    percentFunc?: (value: number, room?: TRoom) => number;
    gradeFunc?: (score: number, value?: number) => number;
    continuityFunc?: (value: any) => number;
    _isComputedTotalScore: boolean = false;
    parentJudge?: TLayoutJudge;
    constructor(figure_categories: string[], options: I_CheckRuleOptions = {}) {
        this._figure_categories = figure_categories || [];

        this.weight = 1;
        this.ui_format = [UI_FormatType.Checked, UI_FormatType.Info];
        for (let key in options) {
            (this as any)[key] = (options as any)[key];
        }
        this._isComputedTotalScore = options.computedTotalScore || false;
    }

    get name() {
        return this.ruleName || this.scoreName;
    }

    get parent_name() {
        return this.parentName || null;
    }

    getMainFigures(figure_elements: TFigureElement[]) {
        figure_elements =
            TBaseRoomToolUtil.instance.getAllSingleFigureFromGroup(figure_elements) || [];
        return figure_elements.filter(ele =>
            compareNames([ele.category, ele.sub_category], this._figure_categories, false)
        );
    }

    isChecked(ans: I_LayoutScore) {
        return ans.score > -0.1;
    }

    getInfoText(ans: I_LayoutScore) {
        return this.name;
    }

    getGrade(ans: I_LayoutScore) {
        if (this.gradeFunc) {
            return this.gradeFunc(ans.score, ans.value);
        } else {
            return 0;
        }
    }

    getPercent(ans: I_LayoutScore, room: TRoom = null) {
        if (this.percentFunc) {
            return this.percentFunc(ans.value);
        } else {
            return ans.score;
        }
    }

    /**
     * 计算得分
     *     --- 可能会加分，也可能会减分
     *     --- 一般+3、-5、-100之类的 整数分数
     * @param room
     * @param figure_elements
     * @returns
     */
    checkScore(room: TRoom, figure_elements: TFigureElement[] = null) {
        let sumScoreInfo: any = this.checkValue(room, figure_elements);
        return this.roomScoreFunc
            ? this.roomScoreFunc(sumScoreInfo.value, room)
            : sumScoreInfo.value;
    }

    /**
     * 计算分值---非计算得分
     *     --- 可能会加分，也可能会减分
     *     --- 一般+3、-5、-100之类的 整数分数
     * @param room
     * @param figure_elements
     * @param layout_scheme
     * @returns
     */
    // TODO 下面这个接口进行一些改造，附带一些额外的信息,detailInfo.score, trimtypes, trimfigures, trimvalue
    checkValue(
        room: TRoom,
        figure_elements: TFigureElement[] = null,
        layout_scheme: TRoomLayoutScheme = null
    ): any {
        let sum_score = 0;
        let main_figures = this.getMainFigures(figure_elements);
        this.preProcessFigures(room, main_figures);
        this.setParamConfig();
        let fineTuningFigures: TFigureElement[] = [];
        let fineTuningTypes: TrimType[] = [];
        let fineTuningValue: number = 0;
        if (!this._isComputedTotalScore) {
            main_figures.forEach(figure => {
                let detailInfo: any = this.checkFigureScore(room, figure, main_figures);
                if (detailInfo == null) return;
                let score: number = detailInfo.score == undefined ? detailInfo : detailInfo.score;
                sum_score +=
                    this._use_score_func && this.figureScoreFunc
                        ? this.figureScoreFunc(score, figure)
                        : score;
                if (detailInfo?.fineTuningFigures) {
                    fineTuningFigures.push(...detailInfo.fineTuningFigures);
                }
                if (detailInfo?.fineTuningTypes) {
                    fineTuningTypes.push(...detailInfo.fineTuningTypes);
                }
                if (detailInfo?.indexValue) {
                    fineTuningValue += this.continuityFunc(detailInfo.indexValue);
                }
            });
        } else {
            let detailInfo: any = this.checkFigureScore(room, null, main_figures);
            let score: number = detailInfo.score == undefined ? detailInfo : detailInfo.score;
            sum_score +=
                this._use_score_func && this.figureScoreFunc
                    ? this.figureScoreFunc(score, null)
                    : score;
            if (detailInfo?.fineTuningFigures) {
                fineTuningFigures.push(...detailInfo.fineTuningFigures);
            }
            if (detailInfo.fineTuningTypes) {
                fineTuningTypes.push(...detailInfo.fineTuningTypes);
            }
            if (detailInfo?.indexValue != undefined) {
                if (this.continuityFunc) {
                    fineTuningValue += this.continuityFunc(detailInfo.indexValue);
                }
            }
        }

        return {
            value: sum_score,
            fineTuningFigures: fineTuningFigures,
            fineTuningTypes: fineTuningTypes,
            fineTuningValue: fineTuningValue
        };
    }

    protected setParamConfig() {}

    computeLayoutScore(
        room: TRoom,
        figure_elements: TFigureElement[] = null,
        order_index: number = -1,
        layout_scheme: TRoomLayoutScheme = null
    ): I_LayoutScore {
        let valueInfo = this.checkValue(room, figure_elements, layout_scheme);
        let score = this.roomScoreFunc
            ? this.roomScoreFunc(valueInfo.value, room)
            : valueInfo.value;
        let ans: I_LayoutScore = {
            name: this.ruleName || "",
            order: order_index,
            score: score,
            value: valueInfo.value,
            fineTuningFigures: valueInfo.fineTuningFigures,
            fineTuningTypes: valueInfo.fineTuningTypes,
            fineTuningValue: valueInfo.fineTuningValue
        };
        if (this.ui_format) {
            ans.ui_format = this.ui_format;
            this.updateLyaoutScoreByFormats(ans, room);
        }

        return ans;
    }

    protected updateLyaoutScoreByFormats(ans: I_LayoutScore, room: TRoom) {
        if (ans.ui_format) {
            let score = ans.score;
            for (let format of this.ui_format) {
                if (format === UI_FormatType.Checked) {
                    ans.checked = this.isChecked(ans);
                } else if (format === UI_FormatType.Info) {
                    ans.info = this.getInfoText(ans);
                } else if (format === UI_FormatType.Grade || format === UI_FormatType.Stars) {
                    ans.grade = this.getGrade(ans);
                } else if (format === UI_FormatType.Percentage) {
                    ans.percent = this.getPercent(ans, room);
                } else {
                    ans.info = this.getInfoText(ans);
                }
            }
        }
    }

    /**
     * 计算细节得分
     *   --- 更平滑的得分
     * @param room
     * @param figure_elements
     */
    checkFigureScore(
        room: TRoom,
        figure_element: TFigureElement,
        main_figures?: TFigureElement[]
    ): any {
        let fineTuningFigures: TFigureElement[] = null;
        let fineTuningTypes: TrimType[] = null;
        let indexValue: 0;
        let score: number = 1.0;
        return {
            fineTuningFigures: fineTuningFigures,
            fineTuningTypes: fineTuningTypes,
            indexValue: indexValue,
            score: score
        };
    }

    /**
     * 对过滤后的数据做个处理，方便子类进行操作
     */
    protected preProcessFigures(room: TRoom, figureElements: TFigureElement[]) {}
}
