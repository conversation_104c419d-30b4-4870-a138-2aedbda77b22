import { Box3, Vector3 } from "three";
import { TPainter } from "../../Drawing/TPainter";
import { ZRect } from "@layoutai/z_polygon";
import { I_BaseGroupData, TBaseGroupEntity } from "../TLayoutEntities/TBaseGroupEntity";
import { TFurnitureEntity } from "../TLayoutEntities/TFurnitureEntity";
import { TLayoutEntityContainer } from "../TLayoutEntities/TLayoutEntityContainter";
import { DrawingFigureMode, KeyPolyTargetType } from "../IRoomInterface";
import { T_GroupOperationInfo } from "@/Apps/LayoutAI/OperationInfos/Operations/T_GroupOpertaionInfo";
import { I_SelectedTarget } from "../TEntitySelector/TEntitySelector";
import { TBaseEntity } from "../TLayoutEntities/TBaseEntity";
export interface I_CombinationTarget
{
    _draw_combination_entitys: TFurnitureEntity[];
    _layout_combination_entitys: TFurnitureEntity[];
    _matched_combination_entitys: TFurnitureEntity[];
    draw_group_rect?: ZRect;
    _all_sub_entitys?: TFurnitureEntity[];
    _group_entity?: TBaseGroupEntity;
}

/**
 *  实体组合器 2025.05.12
 *  --- 把组合相关的操作放在这里
 */
export class TEntityCombiner
{
    protected _container : TLayoutEntityContainer;

    protected _combination_target : I_CombinationTarget;
    constructor(container:TLayoutEntityContainer)
    {
        this._container = container;
        this._combination_target = {
            _layout_combination_entitys: [],
            _matched_combination_entitys: [],
            _draw_combination_entitys: [],
            draw_group_rect: null,
            _all_sub_entitys: []
        }
    }
    get container()
    {
        return this._container;
    }

    get manager()
    {
        return this._container.manager;
    }

    get combination_target()
    {
        return this._combination_target;
    }

    set combination_target(t:I_CombinationTarget)
    {
        this._combination_target = t;
    }

    static copyCombinationTarget(srcObj:I_CombinationTarget,targetObj:I_CombinationTarget)
    {
        targetObj._draw_combination_entitys = [...srcObj._draw_combination_entitys];
        targetObj._layout_combination_entitys = [...srcObj._layout_combination_entitys];
        targetObj._matched_combination_entitys = [...srcObj._matched_combination_entitys];
    }
    static copyCombinationTargetToGroupData(srcObj:I_CombinationTarget,targetObj:I_BaseGroupData)
    {
        targetObj.combination_entitys = [...srcObj._draw_combination_entitys];
        targetObj.layout_combination_entitys = [...srcObj._layout_combination_entitys];
        targetObj.matched_combination_entitys = [...srcObj._matched_combination_entitys];
    }

    drawCanvas(painter:TPainter)
    {
        if (this.combination_target._all_sub_entitys.length > 0) {
            let bbox = new Box3();
            for (let entity of this.combination_target._all_sub_entitys) {
                if (entity.type=== 'BaseGroup') continue;
                let rect = this.manager.layout_container.drawing_figure_mode === DrawingFigureMode.Figure2D ? entity.rect : entity.matched_rect || entity.rect;
                for (let v of rect.vertices) {
                    bbox.expandByPoint((v.pos));
                }
            }

            const t_nor: Vector3 = this.combination_target.draw_group_rect.nor.clone();
            let copy_rect = this.combination_target.draw_group_rect.clone();
            this.combination_target.draw_group_rect = ZRect.fromBox3(bbox, t_nor);
            this.combination_target.draw_group_rect.ex_prop = copy_rect.ex_prop;

            this._combination_target.draw_group_rect.ex_prop[KeyPolyTargetType] = "BaseGroup";

            painter.fillStyle = '#147FFA'
            for (let edge of this.combination_target.draw_group_rect.edges) {
                painter.drawLineDashSegment(edge.v0.pos, edge.v1.pos, '#147FFA');
            }
        }
    }


    // 重组Base_Group
    reGenerateBaseGroup() {
        if (this.combination_target._draw_combination_entitys.length == 0) return;
        let opertion_info = new T_GroupOperationInfo(this.manager as any, "AllGroup");
        if (this.container._drawing_layer_mode === "AIMatching") {
            this.combination_target._draw_combination_entitys.forEach((fe) => {
                fe.figure_element.updateMatchedMaterialByMatchedRect();
            });
        }
        if (this.combination_target._group_entity.isMatchedVisible()) {
            this.combination_target._group_entity._matched_combination_entitys = [...this.combination_target._draw_combination_entitys];
        }

        let draw_group_rect = this.combination_target.draw_group_rect;
        TEntityCombiner.copyCombinationTargetToGroupData(this.combination_target,draw_group_rect._attached_elements as any);

        if (this.combination_target._group_entity.isMatchedVisible()) {
            this._combination_target._group_entity.matched_rect.copy(this.combination_target.draw_group_rect);
        } 
        this.combination_target._group_entity._rect.copy(this._combination_target.draw_group_rect);
        

        this.combination_target._group_entity.recordBaseGroup(this.combination_target._group_entity);
        opertion_info._group_base_entity = this.combination_target._group_entity;
        opertion_info._furniture_entities = this.combination_target._group_entity.combination_entitys;
        this.combination_target._draw_combination_entitys = [];
        this.combination_target._all_sub_entitys = [];
        this.combination_target._group_entity = null;
        opertion_info.redo();

    }
    addBaseGroupEntity(event_param: string, select_target:I_SelectedTarget) {
        let group_rect = new ZRect(1, 1);
        let combination_rect =select_target.selected_rect;
        group_rect._w = combination_rect._w;
        group_rect._h = combination_rect._h;
        group_rect._nor = combination_rect._nor.clone();
        TBaseEntity.set_polygon_type(group_rect,"BaseGroup");

        group_rect.ex_prop['GroupName'] = event_param;
        group_rect.rect_center = combination_rect.rect_center.clone();
        group_rect._attached_elements['combination_entitys'] = [...select_target.selected_combination_entitys];
        group_rect.updateRect();
        let baseGroup_entity = TBaseGroupEntity.getOrMakeEntityOfCadRect(group_rect) as TBaseGroupEntity;
        baseGroup_entity.combination_entitys = [...select_target.selected_combination_entitys];
        baseGroup_entity.updateCategory(event_param);
        let info = new T_GroupOperationInfo(this.manager as any, "Group");
        info._group_base_entity = baseGroup_entity;
        info._furniture_entities =select_target.selected_combination_entitys.map((entity: TFurnitureEntity) => {
            TBaseGroupEntity.recordGroupRectData(entity, baseGroup_entity);
            return entity;
        }
        );
        info.redo();

        this.manager.appendOperationInfo(info);
    }

}