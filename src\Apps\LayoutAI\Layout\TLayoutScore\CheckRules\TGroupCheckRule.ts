import { UI_FormatType } from "../../IUIInterface";
import { TFigureElement } from "../../TFigureElements/TFigureElement";
import { TRoomLayoutScheme } from "../../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../../TRoom";
import { I_LayoutScore } from "../TLayoutJudge";
import { I_CheckRuleOptions, TCheckRule } from "./TCheckRule";

export class TGroupCheckRule extends TCheckRule {
    _children: TCheckRule[];
    constructor(ruleName: string, children: TCheckRule[], options: I_CheckRuleOptions = {}) {
        super([], options);
        if (!options.ui_format) {
            this.ui_format = [UI_FormatType.Name, UI_FormatType.Score];
        }
        this.ruleName = ruleName;
        this._children = [...children];
        this._children = this._children.filter(ele => ele);
    }

    checkScore(room: TRoom, figure_elements?: TFigureElement[]): number {
        let ans_score = 0;
        this._children.forEach(sub_rule => {
            let score = sub_rule.checkScore(room, figure_elements);
            ans_score += score * sub_rule.weight;
        });
        return ans_score;
    }

    computeLayoutScore(
        room: TRoom,
        figure_elements?: TFigureElement[],
        order_index?: number,
        layout_scheme: TRoomLayoutScheme = null
    ): I_LayoutScore {
        let ans: I_LayoutScore = {
            name: this.ruleName || "",
            order: order_index,
            score: 0,
            value: 0,
            children: []
        };

        this._children.forEach((sub_rule, index) => {
            let sub_score = sub_rule.computeLayoutScore(
                room,
                figure_elements,
                index,
                layout_scheme
            );
            ans.score += sub_score.score * sub_rule.weight;
            ans.value += sub_score.value * sub_rule.weight;
            ans.children.push(sub_score);
        });
        if (this.ui_format) {
            ans.ui_format = this.ui_format;
            this.updateLyaoutScoreByFormats(ans, room);
        }

        return ans;
    }
}
